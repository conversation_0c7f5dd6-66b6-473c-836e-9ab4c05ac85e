// 航点数据模型定义
export class Waypoint {
  constructor(options = {}) {
    this.id = options.id || this.generateId()
    this.name = options.name || `航点${this.id}`
    this.type = options.type || 'WAYPOINT' // WAYPOINT, TAKEOFF, LANDING, RTL, LOITER
    
    // 地理坐标 (WGS84)
    this.latitude = options.latitude || 0
    this.longitude = options.longitude || 0
    this.altitude = options.altitude || 0 // 海拔高度(m)
    this.relativeAltitude = options.relativeAltitude || 0 // 相对起飞点高度(m)
    
    // 飞行参数
    this.speed = options.speed || 5.0 // 目标速度(m/s)
    this.heading = options.heading || 0 // 航向角(度)
    this.acceptanceRadius = options.acceptanceRadius || 2.0 // 接受半径(m)
    this.holdTime = options.holdTime || 0 // 停留时间(秒)
    
    // 相机参数
    this.gimbalPitch = options.gimbalPitch || 0 // 云台俯仰角
    this.gimbalYaw = options.gimbalYaw || 0 // 云台偏航角
    this.cameraAction = options.cameraAction || 'NONE' // NONE, PHOTO, VIDEO_START, VIDEO_STOP
    
    // 任务参数
    this.sequence = options.sequence || 0 // 序列号
    this.isActive = options.isActive || false // 是否激活
    this.autocontinue = options.autocontinue !== false // 自动继续
    
    // 状态信息
    this.status = options.status || 'PENDING' // PENDING, ACTIVE, COMPLETED, FAILED, SKIPPED
    this.createdAt = options.createdAt || new Date()
    this.updatedAt = options.updatedAt || new Date()
    
    // 扩展属性
    this.metadata = options.metadata || {}
    this.tags = options.tags || []
  }
  
  generateId() {
    return 'wp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }
  
  // 更新位置
  updatePosition(lat, lng, alt = null) {
    this.latitude = lat
    this.longitude = lng
    if (alt !== null) this.altitude = alt
    this.updatedAt = new Date()
  }
  
  // 计算到另一个航点的距离
  distanceTo(otherWaypoint) {
    const R = 6371000 // 地球半径(米)
    const lat1 = this.latitude * Math.PI / 180
    const lat2 = otherWaypoint.latitude * Math.PI / 180
    const deltaLat = (otherWaypoint.latitude - this.latitude) * Math.PI / 180
    const deltaLng = (otherWaypoint.longitude - this.longitude) * Math.PI / 180
    
    const a = Math.sin(deltaLat/2) * Math.sin(deltaLat/2) +
              Math.cos(lat1) * Math.cos(lat2) *
              Math.sin(deltaLng/2) * Math.sin(deltaLng/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    
    return R * c
  }
  
  // 验证航点数据
  validate() {
    const errors = []
    
    if (!this.latitude || this.latitude < -90 || this.latitude > 90) {
      errors.push('纬度必须在-90到90度之间')
    }
    
    if (!this.longitude || this.longitude < -180 || this.longitude > 180) {
      errors.push('经度必须在-180到180度之间')
    }
    
    if (this.altitude < -1000 || this.altitude > 10000) {
      errors.push('高度必须在-1000到10000米之间')
    }
    
    if (this.speed < 0 || this.speed > 30) {
      errors.push('速度必须在0到30m/s之间')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
  
  // 转换为GeoJSON格式
  toGeoJSON() {
    return {
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: [this.longitude, this.latitude, this.altitude]
      },
      properties: {
        id: this.id,
        name: this.name,
        type: this.type,
        sequence: this.sequence,
        status: this.status,
        speed: this.speed,
        heading: this.heading,
        holdTime: this.holdTime,
        metadata: this.metadata
      }
    }
  }
  
  // 从GeoJSON创建航点
  static fromGeoJSON(geojson) {
    const coords = geojson.geometry.coordinates
    const props = geojson.properties
    
    return new Waypoint({
      id: props.id,
      name: props.name,
      type: props.type,
      latitude: coords[1],
      longitude: coords[0],
      altitude: coords[2] || 0,
      sequence: props.sequence,
      status: props.status,
      speed: props.speed,
      heading: props.heading,
      holdTime: props.holdTime,
      metadata: props.metadata
    })
  }
  
  // 克隆航点
  clone() {
    return new Waypoint({
      ...this,
      id: this.generateId(),
      name: this.name + '_copy',
      createdAt: new Date(),
      updatedAt: new Date()
    })
  }
}

// 任务类定义
export class Mission {
  constructor(options = {}) {
    this.id = options.id || this.generateId()
    this.name = options.name || `任务${this.id}`
    this.description = options.description || ''
    
    // 任务参数
    this.waypoints = options.waypoints || []
    this.homePosition = options.homePosition || null
    this.takeoffAltitude = options.takeoffAltitude || 10
    this.defaultSpeed = options.defaultSpeed || 5.0
    this.rtlAltitude = options.rtlAltitude || 30
    
    // 任务状态
    this.status = options.status || 'DRAFT' // DRAFT, READY, RUNNING, PAUSED, COMPLETED, FAILED
    this.currentWaypointIndex = options.currentWaypointIndex || 0
    this.progress = options.progress || 0 // 0-100
    
    // 时间信息
    this.createdAt = options.createdAt || new Date()
    this.updatedAt = options.updatedAt || new Date()
    this.startedAt = options.startedAt || null
    this.completedAt = options.completedAt || null
    
    // 统计信息
    this.totalDistance = options.totalDistance || 0
    this.estimatedDuration = options.estimatedDuration || 0
    this.actualDuration = options.actualDuration || 0
    
    // 扩展属性
    this.metadata = options.metadata || {}
    this.tags = options.tags || []
  }
  
  generateId() {
    return 'mission_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }
  
  // 添加航点
  addWaypoint(waypoint, index = null) {
    if (index === null) {
      this.waypoints.push(waypoint)
      waypoint.sequence = this.waypoints.length - 1
    } else {
      this.waypoints.splice(index, 0, waypoint)
      this.resequenceWaypoints()
    }
    this.updateStatistics()
    this.updatedAt = new Date()
  }
  
  // 移除航点
  removeWaypoint(waypointId) {
    const index = this.waypoints.findIndex(wp => wp.id === waypointId)
    if (index !== -1) {
      this.waypoints.splice(index, 1)
      this.resequenceWaypoints()
      this.updateStatistics()
      this.updatedAt = new Date()
      return true
    }
    return false
  }
  
  // 重新排序航点
  resequenceWaypoints() {
    this.waypoints.forEach((waypoint, index) => {
      waypoint.sequence = index
    })
  }
  
  // 更新统计信息
  updateStatistics() {
    this.totalDistance = this.calculateTotalDistance()
    this.estimatedDuration = this.calculateEstimatedDuration()
  }
  
  // 计算总距离
  calculateTotalDistance() {
    let totalDistance = 0
    for (let i = 1; i < this.waypoints.length; i++) {
      totalDistance += this.waypoints[i-1].distanceTo(this.waypoints[i])
    }
    return totalDistance
  }
  
  // 计算预估时间
  calculateEstimatedDuration() {
    let totalTime = 0
    for (let i = 1; i < this.waypoints.length; i++) {
      const distance = this.waypoints[i-1].distanceTo(this.waypoints[i])
      const speed = this.waypoints[i].speed || this.defaultSpeed
      totalTime += distance / speed + this.waypoints[i].holdTime
    }
    return totalTime
  }
  
  // 验证任务
  validate() {
    const errors = []
    
    if (this.waypoints.length < 2) {
      errors.push('任务至少需要2个航点')
    }
    
    // 验证每个航点
    this.waypoints.forEach((waypoint, index) => {
      const validation = waypoint.validate()
      if (!validation.isValid) {
        errors.push(`航点${index + 1}: ${validation.errors.join(', ')}`)
      }
    })
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
  
  // 转换为GeoJSON
  toGeoJSON() {
    return {
      type: 'FeatureCollection',
      properties: {
        id: this.id,
        name: this.name,
        description: this.description,
        status: this.status,
        totalDistance: this.totalDistance,
        estimatedDuration: this.estimatedDuration
      },
      features: this.waypoints.map(wp => wp.toGeoJSON())
    }
  }
}

// 航点类型常量
export const WAYPOINT_TYPES = {
  WAYPOINT: { name: '普通航点', icon: 'fas fa-map-marker-alt', color: '#3498db' },
  TAKEOFF: { name: '起飞点', icon: 'fas fa-rocket', color: '#2ecc71' },
  LANDING: { name: '降落点', icon: 'fas fa-landing', color: '#e74c3c' },
  RTL: { name: '返航点', icon: 'fas fa-home', color: '#f39c12' },
  LOITER: { name: '悬停点', icon: 'fas fa-pause-circle', color: '#9b59b6' },
  PHOTO: { name: '拍照点', icon: 'fas fa-camera', color: '#1abc9c' },
  VIDEO_START: { name: '录像开始', icon: 'fas fa-video', color: '#e67e22' },
  VIDEO_STOP: { name: '录像结束', icon: 'fas fa-stop', color: '#95a5a6' }
}

// 任务状态常量
export const MISSION_STATUS = {
  DRAFT: { name: '草稿', color: '#95a5a6' },
  READY: { name: '就绪', color: '#3498db' },
  RUNNING: { name: '执行中', color: '#2ecc71' },
  PAUSED: { name: '暂停', color: '#f39c12' },
  COMPLETED: { name: '已完成', color: '#27ae60' },
  FAILED: { name: '失败', color: '#e74c3c' }
}
