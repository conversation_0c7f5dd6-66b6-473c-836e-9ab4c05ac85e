<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="400.667px" height="400.666px" viewBox="0 0 400.667 400.666" enable-background="new 0 0 400.667 400.666"
	 xml:space="preserve">
<circle fill="#232323" cx="200.333" cy="200" r="161"/>
<path fill="none" stroke="#F9FF00" stroke-width="10" stroke-miterlimit="10" d="M57.4,175.086
	C43.974,251.226,91.471,314.98,149.825,336"/>
<path fill="none" stroke="#FF0000" stroke-width="10" stroke-miterlimit="10" d="M106.854,89.039
	c-28.113,23.588-44.001,55.344-49.512,86.375"/>
<path fill="none" stroke="#007511" stroke-width="10" stroke-miterlimit="10" d="M151.628,336.661
	c40.369,14.45,85.524,10.282,121.509-10.494c62.801-36.257,97.126-122.438,53.195-198.528"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="296.814" y1="85.02" x2="281.752" y2="102.968"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="310.03" y1="136.667" x2="330.321" y2="124.953"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="348.149" y1="173.938" x2="325.076" y2="178.006"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="348.149" y1="226.065" x2="325.076" y2="221.997"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="330.32" y1="275.05" x2="310.03" y2="263.336"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="296.812" y1="314.982" x2="281.752" y2="297.035"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="251.667" y1="341.046" x2="243.655" y2="319.031"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="200.331" y1="350.098" x2="200.332" y2="326.67"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="148.995" y1="341.045" x2="157.008" y2="319.03"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="103.85" y1="314.98" x2="118.911" y2="297.034"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="70.343" y1="275.047" x2="90.633" y2="263.334"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="52.515" y1="226.063" x2="75.588" y2="221.995"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="52.516" y1="173.934" x2="75.588" y2="178.004"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="70.346" y1="124.949" x2="90.635" y2="136.666"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="103.854" y1="85.018" x2="118.912" y2="102.967"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="315.313" y1="103.523" x2="303.075" y2="113.797"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="251.669" y1="58.956" x2="243.655" y2="80.973"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="275.381" y1="70.017" x2="267.395" y2="83.855"/>
<line fill="none" stroke="#FFFFFF" stroke-width="4" stroke-miterlimit="10" x1="200.333" y1="49.904" x2="200.333" y2="73.334"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="226.398" y1="52.188" x2="223.626" y2="67.923"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="213.417" y1="50.478" x2="212.444" y2="61.619"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="239.182" y1="55.021" x2="236.289" y2="65.824"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="263.767" y1="63.969" x2="259.042" y2="74.105"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="286.424" y1="77.05" x2="280.01" y2="86.212"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="341.376" y1="148.667" x2="326.362" y2="154.136"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="350.427" y1="200.002" x2="334.448" y2="200.006"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="341.375" y1="251.337" x2="326.358" y2="245.876"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="315.312" y1="296.48" x2="303.067" y2="286.213"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="275.38" y1="329.987" x2="267.385" y2="316.15"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="226.396" y1="347.816" x2="223.615" y2="332.079"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="174.268" y1="347.816" x2="177.038" y2="332.077"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="125.284" y1="329.988" x2="133.27" y2="316.145"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="85.351" y1="296.481" x2="97.59" y2="286.203"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="59.287" y1="251.337" x2="74.303" y2="245.864"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="50.235" y1="200" x2="66.217" y2="199.994"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="59.287" y1="148.664" x2="74.307" y2="154.125"/>
<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" x1="85.351" y1="103.52" x2="97.598" y2="113.788"/>
<text transform="matrix(1 0 0 1 257.6592 123.6489)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="0.5" stroke-miterlimit="10" font-family="sans-serif" font-size="20">20</text>
<text transform="matrix(1 0 0 1 292.1592 187.1489)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="0.5" stroke-miterlimit="10" font-family="sans-serif" font-size="20">40</text>
<text transform="matrix(1 0 0 1 267.3848 263.3359)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="0.5" stroke-miterlimit="10" font-family="sans-serif" font-size="20">60</text>
<text transform="matrix(1 0 0 1 225.0059 302.1489)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="0.5" stroke-miterlimit="10" font-family="sans-serif" font-size="20">80</text>
<text transform="matrix(1 0 0 1 153.0015 302.1489)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="0.5" stroke-miterlimit="10" font-family="sans-serif" font-size="20">100</text>
<text transform="matrix(1 0 0 1 107.1592 256.6489)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="0.5" stroke-miterlimit="10" font-family="sans-serif" font-size="20">120</text>
<text transform="matrix(1 0 0 1 91.4746 184.6489)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="0.5" stroke-miterlimit="10" font-family="sans-serif" font-size="20">140</text>
<text transform="matrix(1 0 0 1 125.2837 123.6489)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="0.5" stroke-miterlimit="10" font-family="sans-serif" font-size="20">160</text>
<text transform="matrix(1 0 0 1 175.3799 242.1895)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="0.5" stroke-miterlimit="10" font-family="sans-serif" font-size="14">KNOTS</text>
<text transform="matrix(1 0 0 1 161.3086 165.1895)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="0.5" stroke-miterlimit="10" font-family="sans-serif" font-size="14">AIR SPEED</text>
<text transform="matrix(1 0 0 1 194.6592 99.6489)" fill="#FFFFFF" stroke="#FFFFFF" stroke-width="0.5" stroke-miterlimit="10" font-family="sans-serif" font-size="20">0</text>
</svg>
