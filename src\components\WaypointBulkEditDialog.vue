<template>
  <div class="dialog-overlay" @click="handleOverlayClick">
    <div class="waypoint-bulk-edit-dialog" @click.stop>
      <div class="dialog-header">
        <h3>批量编辑航点 ({{ waypoints.length }}个)</h3>
        <button @click="$emit('close')" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="dialog-content">
        <div class="selection-summary">
          <h4>选中的航点</h4>
          <div class="waypoint-chips">
            <div v-for="waypoint in waypoints" :key="waypoint.id" class="waypoint-chip">
              <span class="chip-name">{{ waypoint.name }}</span>
              <span class="chip-type" :style="{ color: getTypeInfo(waypoint.type).color }">
                <i :class="getTypeInfo(waypoint.type).icon"></i>
              </span>
            </div>
          </div>
        </div>
        
        <form @submit.prevent="applyChanges">
          <div class="edit-sections">
            <!-- 批量操作选项 -->
            <div class="edit-section">
              <h4>批量操作</h4>
              <div class="operation-grid">
                <label class="operation-item">
                  <input type="checkbox" v-model="operations.changeType" />
                  <span>修改类型</span>
                  <select v-model="form.type" :disabled="!operations.changeType">
                    <option v-for="(info, type) in WAYPOINT_TYPES" :key="type" :value="type">
                      {{ info.name }}
                    </option>
                  </select>
                </label>
                
                <label class="operation-item">
                  <input type="checkbox" v-model="operations.changeStatus" />
                  <span>修改状态</span>
                  <select v-model="form.status" :disabled="!operations.changeStatus">
                    <option value="PENDING">待执行</option>
                    <option value="ACTIVE">执行中</option>
                    <option value="COMPLETED">已完成</option>
                    <option value="FAILED">失败</option>
                    <option value="SKIPPED">跳过</option>
                  </select>
                </label>
                
                <label class="operation-item">
                  <input type="checkbox" v-model="operations.adjustAltitude" />
                  <span>调整高度</span>
                  <div class="altitude-controls">
                    <select v-model="altitudeOperation" :disabled="!operations.adjustAltitude">
                      <option value="set">设置为</option>
                      <option value="add">增加</option>
                      <option value="subtract">减少</option>
                    </select>
                    <input 
                      v-model.number="form.altitudeValue" 
                      type="number" 
                      step="0.1" 
                      :disabled="!operations.adjustAltitude"
                      placeholder="高度值"
                    />
                    <span>米</span>
                  </div>
                </label>
                
                <label class="operation-item">
                  <input type="checkbox" v-model="operations.adjustSpeed" />
                  <span>调整速度</span>
                  <div class="speed-controls">
                    <select v-model="speedOperation" :disabled="!operations.adjustSpeed">
                      <option value="set">设置为</option>
                      <option value="multiply">乘以</option>
                    </select>
                    <input 
                      v-model.number="form.speedValue" 
                      type="number" 
                      step="0.1" 
                      min="0.1"
                      max="30"
                      :disabled="!operations.adjustSpeed"
                      placeholder="速度值"
                    />
                    <span>m/s</span>
                  </div>
                </label>
                
                <label class="operation-item">
                  <input type="checkbox" v-model="operations.setHoldTime" />
                  <span>设置停留时间</span>
                  <input 
                    v-model.number="form.holdTime" 
                    type="number" 
                    step="1" 
                    min="0"
                    :disabled="!operations.setHoldTime"
                    placeholder="秒"
                  />
                </label>
                
                <label class="operation-item">
                  <input type="checkbox" v-model="operations.resequence" />
                  <span>重新排序</span>
                  <select v-model="sequenceOrder" :disabled="!operations.resequence">
                    <option value="distance">按距离排序</option>
                    <option value="altitude">按高度排序</option>
                    <option value="name">按名称排序</option>
                  </select>
                </label>
              </div>
            </div>
            
            <!-- 高级操作 -->
            <div class="edit-section">
              <h4>高级操作</h4>
              <div class="advanced-operations">
                <label class="operation-item">
                  <input type="checkbox" v-model="operations.optimizePath" />
                  <span>路径优化</span>
                  <small>自动优化航点顺序以减少飞行距离</small>
                </label>
                
                <label class="operation-item">
                  <input type="checkbox" v-model="operations.addPrefix" />
                  <span>添加名称前缀</span>
                  <input 
                    v-model="form.namePrefix" 
                    type="text" 
                    :disabled="!operations.addPrefix"
                    placeholder="前缀"
                  />
                </label>
                
                <label class="operation-item">
                  <input type="checkbox" v-model="operations.addSuffix" />
                  <span>添加名称后缀</span>
                  <input 
                    v-model="form.nameSuffix" 
                    type="text" 
                    :disabled="!operations.addSuffix"
                    placeholder="后缀"
                  />
                </label>
              </div>
            </div>
          </div>
          
          <!-- 预览变更 -->
          <div class="preview-section" v-if="hasSelectedOperations">
            <h4>预览变更</h4>
            <div class="preview-content">
              <div class="preview-stats">
                <div class="stat-item">
                  <span class="label">影响航点:</span>
                  <span class="value">{{ waypoints.length }}个</span>
                </div>
                <div class="stat-item">
                  <span class="label">操作数量:</span>
                  <span class="value">{{ selectedOperationsCount }}项</span>
                </div>
              </div>
              
              <div class="preview-operations">
                <div v-for="op in selectedOperationsList" :key="op.key" class="preview-operation">
                  <i :class="op.icon"></i>
                  <span>{{ op.description }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="dialog-actions">
            <button type="button" @click="$emit('close')" class="btn-cancel">
              取消
            </button>
            <button type="submit" class="btn-apply" :disabled="!hasSelectedOperations">
              应用更改
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useWaypointStore } from '@/stores/WaypointStore.js'
import { WAYPOINT_TYPES } from '@/models/WaypointModels.js'
import { PathPlanningUtils } from '@/utils/PathPlanning.js'

export default {
  name: 'WaypointBulkEditDialog',
  props: {
    waypoints: {
      type: Array,
      required: true
    }
  },
  emits: ['close', 'updated'],
  setup(props, { emit }) {
    const waypointStore = useWaypointStore()
    
    // 操作选项
    const operations = ref({
      changeType: false,
      changeStatus: false,
      adjustAltitude: false,
      adjustSpeed: false,
      setHoldTime: false,
      resequence: false,
      optimizePath: false,
      addPrefix: false,
      addSuffix: false
    })
    
    // 表单数据
    const form = ref({
      type: 'WAYPOINT',
      status: 'PENDING',
      altitudeValue: 0,
      speedValue: 5.0,
      holdTime: 0,
      namePrefix: '',
      nameSuffix: ''
    })
    
    // 操作参数
    const altitudeOperation = ref('set')
    const speedOperation = ref('set')
    const sequenceOrder = ref('distance')
    
    // 计算属性
    const hasSelectedOperations = computed(() => {
      return Object.values(operations.value).some(op => op)
    })
    
    const selectedOperationsCount = computed(() => {
      return Object.values(operations.value).filter(op => op).length
    })
    
    const selectedOperationsList = computed(() => {
      const ops = []
      
      if (operations.value.changeType) {
        const typeInfo = WAYPOINT_TYPES[form.value.type]
        ops.push({
          key: 'changeType',
          icon: 'fas fa-tag',
          description: `将类型更改为 ${typeInfo.name}`
        })
      }
      
      if (operations.value.changeStatus) {
        ops.push({
          key: 'changeStatus',
          icon: 'fas fa-flag',
          description: `将状态更改为 ${getStatusText(form.value.status)}`
        })
      }
      
      if (operations.value.adjustAltitude) {
        const opText = altitudeOperation.value === 'set' ? '设置为' : 
                      altitudeOperation.value === 'add' ? '增加' : '减少'
        ops.push({
          key: 'adjustAltitude',
          icon: 'fas fa-mountain',
          description: `${opText} ${form.value.altitudeValue}米高度`
        })
      }
      
      if (operations.value.adjustSpeed) {
        const opText = speedOperation.value === 'set' ? '设置为' : '乘以'
        ops.push({
          key: 'adjustSpeed',
          icon: 'fas fa-tachometer-alt',
          description: `${opText} ${form.value.speedValue}m/s速度`
        })
      }
      
      if (operations.value.setHoldTime) {
        ops.push({
          key: 'setHoldTime',
          icon: 'fas fa-clock',
          description: `设置停留时间为 ${form.value.holdTime}秒`
        })
      }
      
      if (operations.value.resequence) {
        const orderText = sequenceOrder.value === 'distance' ? '距离' :
                         sequenceOrder.value === 'altitude' ? '高度' : '名称'
        ops.push({
          key: 'resequence',
          icon: 'fas fa-sort',
          description: `按${orderText}重新排序`
        })
      }
      
      if (operations.value.optimizePath) {
        ops.push({
          key: 'optimizePath',
          icon: 'fas fa-route',
          description: '优化航点路径顺序'
        })
      }
      
      if (operations.value.addPrefix && form.value.namePrefix) {
        ops.push({
          key: 'addPrefix',
          icon: 'fas fa-plus',
          description: `添加前缀 "${form.value.namePrefix}"`
        })
      }
      
      if (operations.value.addSuffix && form.value.nameSuffix) {
        ops.push({
          key: 'addSuffix',
          icon: 'fas fa-plus',
          description: `添加后缀 "${form.value.nameSuffix}"`
        })
      }
      
      return ops
    })
    
    // 方法
    function getTypeInfo(type) {
      return WAYPOINT_TYPES[type] || WAYPOINT_TYPES.WAYPOINT
    }
    
    function getStatusText(status) {
      const statusMap = {
        PENDING: '待执行',
        ACTIVE: '执行中',
        COMPLETED: '已完成',
        FAILED: '失败',
        SKIPPED: '跳过'
      }
      return statusMap[status] || status
    }
    
    function applyChanges() {
      try {
        let waypointsToUpdate = [...props.waypoints]
        
        // 路径优化
        if (operations.value.optimizePath) {
          waypointsToUpdate = PathPlanningUtils.optimizeWaypointOrder(waypointsToUpdate)
        }
        
        // 重新排序
        if (operations.value.resequence) {
          waypointsToUpdate = sortWaypoints(waypointsToUpdate, sequenceOrder.value)
        }
        
        // 应用批量更改
        waypointsToUpdate.forEach((waypoint, index) => {
          const updates = {}
          
          // 类型更改
          if (operations.value.changeType) {
            updates.type = form.value.type
          }
          
          // 状态更改
          if (operations.value.changeStatus) {
            updates.status = form.value.status
          }
          
          // 高度调整
          if (operations.value.adjustAltitude) {
            switch (altitudeOperation.value) {
              case 'set':
                updates.altitude = form.value.altitudeValue
                break
              case 'add':
                updates.altitude = waypoint.altitude + form.value.altitudeValue
                break
              case 'subtract':
                updates.altitude = Math.max(0, waypoint.altitude - form.value.altitudeValue)
                break
            }
          }
          
          // 速度调整
          if (operations.value.adjustSpeed) {
            switch (speedOperation.value) {
              case 'set':
                updates.speed = form.value.speedValue
                break
              case 'multiply':
                updates.speed = Math.min(30, waypoint.speed * form.value.speedValue)
                break
            }
          }
          
          // 停留时间
          if (operations.value.setHoldTime) {
            updates.holdTime = form.value.holdTime
          }
          
          // 序列号更新
          if (operations.value.resequence || operations.value.optimizePath) {
            updates.sequence = index
          }
          
          // 名称更新
          let newName = waypoint.name
          if (operations.value.addPrefix && form.value.namePrefix) {
            newName = form.value.namePrefix + newName
          }
          if (operations.value.addSuffix && form.value.nameSuffix) {
            newName = newName + form.value.nameSuffix
          }
          if (newName !== waypoint.name) {
            updates.name = newName
          }
          
          // 应用更新
          if (Object.keys(updates).length > 0) {
            waypointStore.updateWaypoint(waypoint.id, updates)
          }
        })
        
        emit('updated')
      } catch (error) {
        alert(`批量更新失败: ${error.message}`)
      }
    }
    
    function sortWaypoints(waypoints, order) {
      const sorted = [...waypoints]
      
      switch (order) {
        case 'distance':
          // 按距离排序（从第一个点开始的最短路径）
          return PathPlanningUtils.optimizeWaypointOrder(sorted)
        
        case 'altitude':
          return sorted.sort((a, b) => a.altitude - b.altitude)
        
        case 'name':
          return sorted.sort((a, b) => a.name.localeCompare(b.name))
        
        default:
          return sorted
      }
    }
    
    function handleOverlayClick() {
      emit('close')
    }
    
    return {
      operations,
      form,
      altitudeOperation,
      speedOperation,
      sequenceOrder,
      hasSelectedOperations,
      selectedOperationsCount,
      selectedOperationsList,
      WAYPOINT_TYPES,
      getTypeInfo,
      getStatusText,
      applyChanges,
      handleOverlayClick
    }
  }
}
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
  backdrop-filter: blur(5px);
}

.waypoint-bulk-edit-dialog {
  background: rgba(15, 25, 40, 0.95);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 12px;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow-y: auto;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  background: rgba(0, 255, 255, 0.05);
}

.dialog-header h3 {
  margin: 0;
  color: #00ffff;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-size: 16px;
}

.close-btn:hover {
  color: #ff6b47;
  background: rgba(255, 107, 71, 0.1);
}

.dialog-content {
  padding: 24px;
}

.selection-summary {
  margin-bottom: 24px;
  padding: 16px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.selection-summary h4 {
  margin: 0 0 12px 0;
  color: #00ffff;
  font-size: 14px;
  font-weight: 600;
}

.waypoint-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.waypoint-chip {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  font-size: 11px;
}

.chip-name {
  color: #ffffff;
}

.chip-type {
  font-size: 10px;
}

.edit-sections {
  display: grid;
  gap: 24px;
  margin-bottom: 24px;
}

.edit-section {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
}

.edit-section h4 {
  margin: 0 0 16px 0;
  color: #00ffff;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  padding-bottom: 8px;
}

.operation-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.advanced-operations {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.operation-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.operation-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.operation-item input[type="checkbox"] {
  margin-right: 8px;
}

.operation-item > span {
  color: #ffffff;
  font-size: 13px;
  font-weight: 500;
}

.operation-item small {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
}

.altitude-controls,
.speed-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.altitude-controls select,
.speed-controls select,
.operation-item select,
.operation-item input[type="text"],
.operation-item input[type="number"] {
  padding: 6px 8px;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: #ffffff;
  font-size: 12px;
}

.altitude-controls span,
.speed-controls span {
  color: rgba(255, 255, 255, 0.7);
  font-size: 11px;
}

.preview-section {
  background: rgba(0, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.preview-section h4 {
  margin: 0 0 12px 0;
  color: #00ffff;
  font-size: 14px;
  font-weight: 600;
}

.preview-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 12px;
}

.stat-item {
  display: flex;
  gap: 8px;
  font-size: 12px;
}

.stat-item .label {
  color: rgba(255, 255, 255, 0.7);
}

.stat-item .value {
  color: #00ffff;
  font-weight: 600;
}

.preview-operations {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.preview-operation {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
}

.preview-operation i {
  color: #00ffff;
  width: 16px;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-cancel,
.btn-apply {
  padding: 10px 20px;
  border: 1px solid;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-cancel {
  background: rgba(149, 165, 166, 0.1);
  border-color: rgba(149, 165, 166, 0.5);
  color: #95a5a6;
}

.btn-cancel:hover {
  background: rgba(149, 165, 166, 0.2);
  border-color: rgba(149, 165, 166, 0.7);
}

.btn-apply {
  background: rgba(46, 204, 113, 0.1);
  border-color: rgba(46, 204, 113, 0.5);
  color: #2ecc71;
}

.btn-apply:hover:not(:disabled) {
  background: rgba(46, 204, 113, 0.2);
  border-color: rgba(46, 204, 113, 0.7);
}

.btn-apply:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .operation-grid {
    grid-template-columns: 1fr;
  }
  
  .preview-stats {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
