<template>
  <div class="waypoint-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <div class="header-left">
        <i class="fas fa-map-marker-alt"></i>
        <h3>航点管理</h3>
        <span class="waypoint-count">({{ totalWaypoints }})</span>
      </div>
      <div class="header-actions">
        <button @click="toggleBulkMode" class="action-btn" :class="{ active: bulkEditMode }">
          <i class="fas fa-check-square"></i>
        </button>
        <button @click="showCreateDialog" class="action-btn create">
          <i class="fas fa-plus"></i>
        </button>
        <button @click="importWaypoints" class="action-btn">
          <i class="fas fa-upload"></i>
        </button>
        <button @click="exportWaypoints" class="action-btn">
          <i class="fas fa-download"></i>
        </button>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="search-filters">
      <div class="search-box">
        <i class="fas fa-search"></i>
        <input 
          v-model="searchQuery" 
          type="text" 
          placeholder="搜索航点..."
          class="search-input"
        />
        <button v-if="searchQuery" @click="clearSearch" class="clear-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="filter-row">
        <select v-model="filterType" class="filter-select">
          <option value="ALL">所有类型</option>
          <option v-for="(info, type) in WAYPOINT_TYPES" :key="type" :value="type">
            {{ info.name }}
          </option>
        </select>
        
        <select v-model="filterStatus" class="filter-select">
          <option value="ALL">所有状态</option>
          <option value="PENDING">待执行</option>
          <option value="ACTIVE">执行中</option>
          <option value="COMPLETED">已完成</option>
          <option value="FAILED">失败</option>
          <option value="SKIPPED">跳过</option>
        </select>
      </div>
    </div>

    <!-- 航点列表 -->
    <div class="waypoint-list" ref="waypointList">
      <div class="list-header">
        <div class="column-headers">
          <div class="col-select" v-if="bulkEditMode">
            <input 
              type="checkbox" 
              :checked="isAllSelected"
              @change="toggleSelectAll"
              class="select-all-checkbox"
            />
          </div>
          <div class="col-name">名称</div>
          <div class="col-type">类型</div>
          <div class="col-status">状态</div>
          <div class="col-actions">操作</div>
        </div>
      </div>
      
      <div class="list-content">
        <VirtualList
          :items="filteredWaypoints"
          :item-height="60"
          :container-height="400"
          v-slot="{ item: waypoint }"
        >
          <div 
            class="waypoint-item"
            :class="{ 
              selected: selectedWaypoints.has(waypoint.id),
              'bulk-selected': bulkEditSelection.has(waypoint.id)
            }"
            @click="selectWaypoint(waypoint)"
          >
            <!-- 批量选择 -->
            <div class="item-select" v-if="bulkEditMode">
              <input 
                type="checkbox" 
                :checked="bulkEditSelection.has(waypoint.id)"
                @change="toggleBulkSelect(waypoint.id)"
                @click.stop
                class="bulk-checkbox"
              />
            </div>
            
            <!-- 航点信息 -->
            <div class="item-content">
              <div class="item-main">
                <div class="item-header">
                  <div class="waypoint-name">{{ waypoint.name }}</div>
                  <div class="waypoint-sequence">#{{ waypoint.sequence + 1 }}</div>
                </div>
                <div class="item-details">
                  <span class="detail-item">
                    <i class="fas fa-map-marker-alt"></i>
                    {{ formatCoordinates(waypoint) }}
                  </span>
                  <span class="detail-item">
                    <i class="fas fa-mountain"></i>
                    {{ waypoint.altitude }}m
                  </span>
                  <span class="detail-item">
                    <i class="fas fa-tachometer-alt"></i>
                    {{ waypoint.speed }}m/s
                  </span>
                </div>
              </div>
              
              <div class="item-meta">
                <div class="waypoint-type" :style="{ color: getTypeInfo(waypoint.type).color }">
                  <i :class="getTypeInfo(waypoint.type).icon"></i>
                  {{ getTypeInfo(waypoint.type).name }}
                </div>
                <div class="waypoint-status" :class="'status-' + waypoint.status.toLowerCase()">
                  {{ getStatusText(waypoint.status) }}
                </div>
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="item-actions" v-if="!bulkEditMode">
              <button @click.stop="editWaypoint(waypoint)" class="action-btn small edit">
                <i class="fas fa-edit"></i>
              </button>
              <button @click.stop="duplicateWaypoint(waypoint)" class="action-btn small">
                <i class="fas fa-copy"></i>
              </button>
              <button @click.stop="deleteWaypoint(waypoint)" class="action-btn small delete">
                <i class="fas fa-trash"></i>
              </button>
              <button @click.stop="locateWaypoint(waypoint)" class="action-btn small">
                <i class="fas fa-crosshairs"></i>
              </button>
            </div>
          </div>
        </VirtualList>
      </div>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="bulkEditMode && bulkEditSelection.size > 0" class="bulk-actions-bar">
      <div class="selection-info">
        已选择 {{ bulkEditSelection.size }} 个航点
      </div>
      <div class="bulk-buttons">
        <button @click="bulkEdit" class="bulk-action-btn edit">
          <i class="fas fa-edit"></i> 批量编辑
        </button>
        <button @click="bulkDelete" class="bulk-action-btn delete">
          <i class="fas fa-trash"></i> 批量删除
        </button>
        <button @click="createMissionFromSelection" class="bulk-action-btn mission">
          <i class="fas fa-route"></i> 创建任务
        </button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="panel-footer">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ totalWaypoints }}</div>
          <div class="stat-label">总航点</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ activeWaypoints }}</div>
          <div class="stat-label">活跃</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ completedWaypoints }}</div>
          <div class="stat-label">已完成</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ totalDistance.toFixed(1) }}km</div>
          <div class="stat-label">总距离</div>
        </div>
      </div>
    </div>

    <!-- 创建航点对话框 -->
    <WaypointCreateDialog 
      v-if="showCreateWaypointDialog"
      @close="showCreateWaypointDialog = false"
      @created="handleWaypointCreated"
    />

    <!-- 编辑航点对话框 -->
    <WaypointEditDialog 
      v-if="editingWaypoint"
      :waypoint="editingWaypoint"
      @close="editingWaypoint = null"
      @updated="handleWaypointUpdated"
    />

    <!-- 批量编辑对话框 -->
    <WaypointBulkEditDialog 
      v-if="showBulkEditDialog"
      :waypoints="selectedWaypointsForBulkEdit"
      @close="showBulkEditDialog = false"
      @updated="handleBulkUpdated"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useWaypointStore } from '../stores/WaypointStore.js'
import { WAYPOINT_TYPES } from '../models/WaypointModels.js'
import VirtualList from './VirtualList.vue'
import WaypointCreateDialog from './WaypointCreateDialog.vue'
import WaypointEditDialog from './WaypointEditDialog.vue'
import WaypointBulkEditDialog from './WaypointBulkEditDialog.vue'

export default {
  name: 'WaypointPanel',
  components: {
    VirtualList,
    WaypointCreateDialog,
    WaypointEditDialog,
    WaypointBulkEditDialog
  },
  emits: ['waypoint-selected', 'waypoint-located'],
  setup(props, { emit }) {
    const waypointStore = useWaypointStore()
    
    // 响应式数据
    const waypointList = ref(null)
    const showCreateWaypointDialog = ref(false)
    const editingWaypoint = ref(null)
    const showBulkEditDialog = ref(false)
    
    // 计算属性
    const totalWaypoints = computed(() => waypointStore.state.totalWaypoints)
    const selectedWaypoints = computed(() => waypointStore.state.selectedWaypoints)
    const bulkEditMode = computed(() => waypointStore.state.bulkEditMode)
    const bulkEditSelection = computed(() => waypointStore.state.bulkEditSelection)
    const searchQuery = computed({
      get: () => waypointStore.state.searchQuery,
      set: (value) => waypointStore.state.searchQuery = value
    })
    const filterType = computed({
      get: () => waypointStore.state.filterType,
      set: (value) => waypointStore.state.filterType = value
    })
    const filterStatus = computed({
      get: () => waypointStore.state.filterStatus,
      set: (value) => waypointStore.state.filterStatus = value
    })
    
    const filteredWaypoints = computed(() => {
      return waypointStore.filteredWaypoints.value
    })
    
    const isAllSelected = computed(() => {
      return filteredWaypoints.value.length > 0 && 
             filteredWaypoints.value.every(wp => bulkEditSelection.value.has(wp.id))
    })
    
    const selectedWaypointsForBulkEdit = computed(() => {
      return Array.from(bulkEditSelection.value).map(id => 
        waypointStore.state.waypoints.get(id)
      ).filter(Boolean)
    })
    
    // 统计计算
    const activeWaypoints = computed(() => {
      return Array.from(waypointStore.state.waypoints.values())
        .filter(wp => wp.status === 'ACTIVE').length
    })
    
    const completedWaypoints = computed(() => {
      return Array.from(waypointStore.state.waypoints.values())
        .filter(wp => wp.status === 'COMPLETED').length
    })
    
    const totalDistance = computed(() => {
      const waypoints = Array.from(waypointStore.state.waypoints.values())
        .sort((a, b) => a.sequence - b.sequence)
      
      let distance = 0
      for (let i = 1; i < waypoints.length; i++) {
        distance += waypoints[i-1].distanceTo(waypoints[i])
      }
      return distance / 1000 // 转换为公里
    })
    
    // 方法
    function getTypeInfo(type) {
      return WAYPOINT_TYPES[type] || WAYPOINT_TYPES.WAYPOINT
    }
    
    function getStatusText(status) {
      const statusMap = {
        PENDING: '待执行',
        ACTIVE: '执行中',
        COMPLETED: '已完成',
        FAILED: '失败',
        SKIPPED: '跳过'
      }
      return statusMap[status] || status
    }
    
    function formatCoordinates(waypoint) {
      return `${waypoint.latitude.toFixed(6)}, ${waypoint.longitude.toFixed(6)}`
    }
    
    function selectWaypoint(waypoint) {
      if (bulkEditMode.value) {
        toggleBulkSelect(waypoint.id)
      } else {
        waypointStore.state.selectedWaypoints.clear()
        waypointStore.state.selectedWaypoints.add(waypoint.id)
        emit('waypoint-selected', waypoint)
      }
    }
    
    function toggleBulkMode() {
      waypointStore.state.bulkEditMode = !waypointStore.state.bulkEditMode
      if (!waypointStore.state.bulkEditMode) {
        waypointStore.state.bulkEditSelection.clear()
      }
    }
    
    function toggleBulkSelect(waypointId) {
      if (bulkEditSelection.value.has(waypointId)) {
        bulkEditSelection.value.delete(waypointId)
      } else {
        bulkEditSelection.value.add(waypointId)
      }
    }
    
    function toggleSelectAll() {
      if (isAllSelected.value) {
        // 取消全选
        filteredWaypoints.value.forEach(wp => {
          bulkEditSelection.value.delete(wp.id)
        })
      } else {
        // 全选
        filteredWaypoints.value.forEach(wp => {
          bulkEditSelection.value.add(wp.id)
        })
      }
    }
    
    function clearSearch() {
      searchQuery.value = ''
    }
    
    function showCreateDialog() {
      showCreateWaypointDialog.value = true
    }
    
    function editWaypoint(waypoint) {
      editingWaypoint.value = waypoint
    }
    
    function duplicateWaypoint(waypoint) {
      const newWaypoint = waypoint.clone()
      newWaypoint.latitude += 0.0001 // 稍微偏移位置
      newWaypoint.longitude += 0.0001
      waypointStore.createWaypoint(newWaypoint)
    }
    
    function deleteWaypoint(waypoint) {
      if (confirm(`确定要删除航点 "${waypoint.name}" 吗？`)) {
        waypointStore.deleteWaypoint(waypoint.id)
      }
    }
    
    function locateWaypoint(waypoint) {
      emit('waypoint-located', waypoint)
    }
    
    function bulkEdit() {
      showBulkEditDialog.value = true
    }
    
    function bulkDelete() {
      const count = bulkEditSelection.value.size
      if (confirm(`确定要删除选中的 ${count} 个航点吗？`)) {
        waypointStore.deleteWaypointsBatch(Array.from(bulkEditSelection.value))
        bulkEditSelection.value.clear()
      }
    }
    
    function createMissionFromSelection() {
      const waypoints = selectedWaypointsForBulkEdit.value
      if (waypoints.length < 2) {
        alert('至少需要选择2个航点才能创建任务')
        return
      }
      
      try {
        const mission = waypointStore.createMission({
          name: `任务_${Date.now()}`,
          waypoints: waypoints.sort((a, b) => a.sequence - b.sequence)
        })
        
        waypointStore.setCurrentMission(mission.id)
        bulkEditSelection.value.clear()
        waypointStore.state.bulkEditMode = false
        
        alert(`成功创建任务 "${mission.name}"`)
      } catch (error) {
        alert(`创建任务失败: ${error.message}`)
      }
    }
    
    function importWaypoints() {
      // 创建文件输入元素
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.json,.kml,.gpx'
      input.onchange = handleFileImport
      input.click()
    }
    
    function handleFileImport(event) {
      const file = event.target.files[0]
      if (!file) return
      
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target.result)
          // 处理导入的航点数据
          console.log('导入航点数据:', data)
          // TODO: 实现具体的导入逻辑
        } catch (error) {
          alert(`导入失败: ${error.message}`)
        }
      }
      reader.readAsText(file)
    }
    
    function exportWaypoints() {
      const waypoints = Array.from(waypointStore.state.waypoints.values())
      const data = {
        waypoints: waypoints.map(wp => wp.toGeoJSON()),
        exportTime: new Date().toISOString(),
        version: '1.0'
      }
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `waypoints_${Date.now()}.json`
      a.click()
      URL.revokeObjectURL(url)
    }
    
    function handleWaypointCreated(waypoint) {
      showCreateWaypointDialog.value = false
      selectWaypoint(waypoint)
    }
    
    function handleWaypointUpdated(waypoint) {
      editingWaypoint.value = null
    }
    
    function handleBulkUpdated() {
      showBulkEditDialog.value = false
      bulkEditSelection.value.clear()
    }
    
    return {
      // 响应式数据
      waypointList,
      showCreateWaypointDialog,
      editingWaypoint,
      showBulkEditDialog,
      
      // 计算属性
      totalWaypoints,
      selectedWaypoints,
      bulkEditMode,
      bulkEditSelection,
      searchQuery,
      filterType,
      filterStatus,
      filteredWaypoints,
      isAllSelected,
      selectedWaypointsForBulkEdit,
      activeWaypoints,
      completedWaypoints,
      totalDistance,
      
      // 常量
      WAYPOINT_TYPES,
      
      // 方法
      getTypeInfo,
      getStatusText,
      formatCoordinates,
      selectWaypoint,
      toggleBulkMode,
      toggleBulkSelect,
      toggleSelectAll,
      clearSearch,
      showCreateDialog,
      editWaypoint,
      duplicateWaypoint,
      deleteWaypoint,
      locateWaypoint,
      bulkEdit,
      bulkDelete,
      createMissionFromSelection,
      importWaypoints,
      exportWaypoints,
      handleWaypointCreated,
      handleWaypointUpdated,
      handleBulkUpdated
    }
  }
}
</script>
