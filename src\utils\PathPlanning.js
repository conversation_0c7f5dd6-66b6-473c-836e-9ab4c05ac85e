// 路径规划工具类
import { Waypoint } from '@/models/WaypointModels.js'

export class PathPlanner {
  constructor(options = {}) {
    this.options = {
      // 飞行参数
      maxSpeed: options.maxSpeed || 15.0, // 最大速度 m/s
      maxAcceleration: options.maxAcceleration || 2.0, // 最大加速度 m/s²
      maxClimbRate: options.maxClimbRate || 3.0, // 最大爬升率 m/s
      maxDescentRate: options.maxDescentRate || 2.0, // 最大下降率 m/s
      
      // 安全参数
      minAltitude: options.minAltitude || 10, // 最小飞行高度 m
      maxAltitude: options.maxAltitude || 120, // 最大飞行高度 m
      safetyBuffer: options.safetyBuffer || 5, // 安全缓冲距离 m
      
      // 路径优化参数
      smoothingFactor: options.smoothingFactor || 0.5, // 路径平滑因子
      optimizeForTime: options.optimizeForTime || true, // 优化时间还是距离
      
      // 避障参数
      obstacleAvoidance: options.obstacleAvoidance || true,
      avoidanceRadius: options.avoidanceRadius || 20, // 避障半径 m
      
      ...options
    }
    
    // 地形数据缓存
    this.terrainCache = new Map()
    
    // 禁飞区数据
    this.noFlyZones = []
    
    // 障碍物数据
    this.obstacles = []
  }
  
  // ==================== 路径生成 ====================
  
  // 生成基础路径
  generatePath(waypoints, options = {}) {
    if (!waypoints || waypoints.length < 2) {
      throw new Error('至少需要2个航点才能生成路径')
    }
    
    // 验证航点
    this.validateWaypoints(waypoints)
    
    // 排序航点
    const sortedWaypoints = this.sortWaypoints(waypoints)
    
    // 生成路径段
    const pathSegments = this.generatePathSegments(sortedWaypoints, options)
    
    // 路径优化
    const optimizedPath = this.optimizePath(pathSegments, options)
    
    // 添加安全检查点
    const safetyPath = this.addSafetyCheckpoints(optimizedPath, options)
    
    return {
      waypoints: sortedWaypoints,
      segments: safetyPath,
      totalDistance: this.calculateTotalDistance(safetyPath),
      estimatedTime: this.calculateEstimatedTime(safetyPath),
      metadata: {
        generatedAt: new Date(),
        options: { ...this.options, ...options }
      }
    }
  }
  
  // 验证航点
  validateWaypoints(waypoints) {
    const errors = []
    
    waypoints.forEach((waypoint, index) => {
      // 高度检查
      if (waypoint.altitude < this.options.minAltitude) {
        errors.push(`航点${index + 1}高度过低: ${waypoint.altitude}m < ${this.options.minAltitude}m`)
      }
      
      if (waypoint.altitude > this.options.maxAltitude) {
        errors.push(`航点${index + 1}高度过高: ${waypoint.altitude}m > ${this.options.maxAltitude}m`)
      }
      
      // 速度检查
      if (waypoint.speed > this.options.maxSpeed) {
        errors.push(`航点${index + 1}速度过高: ${waypoint.speed}m/s > ${this.options.maxSpeed}m/s`)
      }
      
      // 禁飞区检查
      if (this.isInNoFlyZone(waypoint)) {
        errors.push(`航点${index + 1}位于禁飞区内`)
      }
    })
    
    if (errors.length > 0) {
      throw new Error(`航点验证失败:\n${errors.join('\n')}`)
    }
  }
  
  // 排序航点
  sortWaypoints(waypoints) {
    return [...waypoints].sort((a, b) => a.sequence - b.sequence)
  }
  
  // 生成路径段
  generatePathSegments(waypoints, options) {
    const segments = []
    
    for (let i = 0; i < waypoints.length - 1; i++) {
      const from = waypoints[i]
      const to = waypoints[i + 1]
      
      const segment = this.generateSegment(from, to, options)
      segments.push(segment)
    }
    
    return segments
  }
  
  // 生成单个路径段
  generateSegment(from, to, options) {
    const distance = from.distanceTo(to)
    const altitudeDiff = to.altitude - from.altitude
    
    // 计算基础路径点
    const basePoints = this.interpolatePoints(from, to, options)
    
    // 高度插值
    const pointsWithAltitude = this.interpolateAltitude(basePoints, from.altitude, to.altitude)
    
    // 速度规划
    const pointsWithSpeed = this.planSpeed(pointsWithAltitude, from.speed, to.speed)
    
    // 避障处理
    const safePoints = this.avoidObstacles(pointsWithSpeed, options)
    
    return {
      from: from.id,
      to: to.id,
      points: safePoints,
      distance,
      altitudeDiff,
      estimatedTime: this.calculateSegmentTime(safePoints)
    }
  }
  
  // 插值生成中间点
  interpolatePoints(from, to, options) {
    const distance = from.distanceTo(to)
    const resolution = options.resolution || 10 // 每10米一个点
    const numPoints = Math.max(2, Math.ceil(distance / resolution))
    
    const points = []
    
    for (let i = 0; i <= numPoints; i++) {
      const ratio = i / numPoints
      const lat = from.latitude + (to.latitude - from.latitude) * ratio
      const lng = from.longitude + (to.longitude - from.longitude) * ratio
      
      points.push({
        latitude: lat,
        longitude: lng,
        ratio,
        distance: distance * ratio
      })
    }
    
    return points
  }
  
  // 高度插值
  interpolateAltitude(points, fromAlt, toAlt) {
    return points.map(point => ({
      ...point,
      altitude: fromAlt + (toAlt - fromAlt) * point.ratio
    }))
  }
  
  // 速度规划
  planSpeed(points, fromSpeed, toSpeed) {
    const totalDistance = points[points.length - 1].distance
    
    return points.map((point, index) => {
      let targetSpeed
      
      if (index === 0) {
        targetSpeed = fromSpeed
      } else if (index === points.length - 1) {
        targetSpeed = toSpeed
      } else {
        // 使用梯形速度曲线
        const progress = point.distance / totalDistance
        targetSpeed = this.calculateTrapezoidalSpeed(progress, fromSpeed, toSpeed, totalDistance)
      }
      
      return {
        ...point,
        speed: Math.min(targetSpeed, this.options.maxSpeed)
      }
    })
  }
  
  // 梯形速度曲线计算
  calculateTrapezoidalSpeed(progress, startSpeed, endSpeed, distance) {
    const accelerationDistance = distance * 0.3 // 加速段占30%
    const decelerationDistance = distance * 0.3 // 减速段占30%
    const cruiseDistance = distance * 0.4 // 巡航段占40%
    
    const maxCruiseSpeed = Math.max(startSpeed, endSpeed, this.options.maxSpeed * 0.8)
    
    if (progress < 0.3) {
      // 加速段
      const accelProgress = progress / 0.3
      return startSpeed + (maxCruiseSpeed - startSpeed) * accelProgress
    } else if (progress < 0.7) {
      // 巡航段
      return maxCruiseSpeed
    } else {
      // 减速段
      const decelProgress = (progress - 0.7) / 0.3
      return maxCruiseSpeed + (endSpeed - maxCruiseSpeed) * decelProgress
    }
  }
  
  // 避障处理
  avoidObstacles(points, options) {
    if (!this.options.obstacleAvoidance || this.obstacles.length === 0) {
      return points
    }
    
    return points.map(point => {
      const nearbyObstacles = this.findNearbyObstacles(point, this.options.avoidanceRadius)
      
      if (nearbyObstacles.length === 0) {
        return point
      }
      
      // 计算避障偏移
      const avoidanceOffset = this.calculateAvoidanceOffset(point, nearbyObstacles)
      
      return {
        ...point,
        latitude: point.latitude + avoidanceOffset.lat,
        longitude: point.longitude + avoidanceOffset.lng,
        altitude: Math.max(point.altitude, avoidanceOffset.minAltitude),
        avoidanceApplied: true
      }
    })
  }
  
  // 查找附近障碍物
  findNearbyObstacles(point, radius) {
    return this.obstacles.filter(obstacle => {
      const distance = this.calculateDistance(
        point.latitude, point.longitude,
        obstacle.latitude, obstacle.longitude
      )
      return distance <= radius
    })
  }
  
  // 计算避障偏移
  calculateAvoidanceOffset(point, obstacles) {
    let totalOffsetLat = 0
    let totalOffsetLng = 0
    let maxAltitude = point.altitude
    
    obstacles.forEach(obstacle => {
      const distance = this.calculateDistance(
        point.latitude, point.longitude,
        obstacle.latitude, obstacle.longitude
      )
      
      if (distance < this.options.avoidanceRadius) {
        // 计算远离障碍物的方向
        const bearing = this.calculateBearing(
          obstacle.latitude, obstacle.longitude,
          point.latitude, point.longitude
        )
        
        const avoidanceDistance = this.options.avoidanceRadius - distance + this.options.safetyBuffer
        const offset = this.calculateOffset(bearing, avoidanceDistance)
        
        totalOffsetLat += offset.lat
        totalOffsetLng += offset.lng
        
        // 高度避障
        if (obstacle.height) {
          maxAltitude = Math.max(maxAltitude, obstacle.height + this.options.safetyBuffer)
        }
      }
    })
    
    return {
      lat: totalOffsetLat,
      lng: totalOffsetLng,
      minAltitude: maxAltitude
    }
  }
  
  // ==================== 路径优化 ====================
  
  // 路径优化
  optimizePath(segments, options) {
    let optimizedSegments = [...segments]
    
    // 路径平滑
    if (options.smoothPath !== false) {
      optimizedSegments = this.smoothPath(optimizedSegments)
    }
    
    // 速度优化
    if (options.optimizeSpeed !== false) {
      optimizedSegments = this.optimizeSpeed(optimizedSegments)
    }
    
    // 高度优化
    if (options.optimizeAltitude !== false) {
      optimizedSegments = this.optimizeAltitude(optimizedSegments)
    }
    
    return optimizedSegments
  }
  
  // 路径平滑
  smoothPath(segments) {
    return segments.map(segment => ({
      ...segment,
      points: this.applySmoothingFilter(segment.points)
    }))
  }
  
  // 应用平滑滤波器
  applySmoothingFilter(points) {
    if (points.length < 3) return points
    
    const smoothedPoints = [points[0]] // 保持起点不变
    
    for (let i = 1; i < points.length - 1; i++) {
      const prev = points[i - 1]
      const curr = points[i]
      const next = points[i + 1]
      
      const smoothed = {
        ...curr,
        latitude: prev.latitude * 0.25 + curr.latitude * 0.5 + next.latitude * 0.25,
        longitude: prev.longitude * 0.25 + curr.longitude * 0.5 + next.longitude * 0.25,
        altitude: prev.altitude * 0.25 + curr.altitude * 0.5 + next.altitude * 0.25
      }
      
      smoothedPoints.push(smoothed)
    }
    
    smoothedPoints.push(points[points.length - 1]) // 保持终点不变
    
    return smoothedPoints
  }
  
  // 速度优化
  optimizeSpeed(segments) {
    // 全局速度优化，考虑整个路径的连续性
    return segments.map((segment, index) => {
      const optimizedPoints = segment.points.map((point, pointIndex) => {
        // 考虑转弯半径的速度限制
        const turnRadius = this.calculateTurnRadius(segment, pointIndex)
        const maxTurnSpeed = this.calculateMaxTurnSpeed(turnRadius)
        
        return {
          ...point,
          speed: Math.min(point.speed, maxTurnSpeed)
        }
      })
      
      return {
        ...segment,
        points: optimizedPoints
      }
    })
  }
  
  // 高度优化
  optimizeAltitude(segments) {
    return segments.map(segment => {
      const optimizedPoints = segment.points.map(point => {
        // 地形跟随
        const terrainHeight = this.getTerrainHeight(point.latitude, point.longitude)
        const minSafeAltitude = terrainHeight + this.options.safetyBuffer
        
        return {
          ...point,
          altitude: Math.max(point.altitude, minSafeAltitude),
          terrainHeight
        }
      })
      
      return {
        ...segment,
        points: optimizedPoints
      }
    })
  }
  
  // ==================== 工具函数 ====================
  
  // 计算两点间距离
  calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371000 // 地球半径
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLng = (lng2 - lng1) * Math.PI / 180
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c
  }
  
  // 计算方位角
  calculateBearing(lat1, lng1, lat2, lng2) {
    const dLng = (lng2 - lng1) * Math.PI / 180
    const lat1Rad = lat1 * Math.PI / 180
    const lat2Rad = lat2 * Math.PI / 180
    
    const y = Math.sin(dLng) * Math.cos(lat2Rad)
    const x = Math.cos(lat1Rad) * Math.sin(lat2Rad) -
              Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(dLng)
    
    return Math.atan2(y, x) * 180 / Math.PI
  }
  
  // 计算偏移坐标
  calculateOffset(bearing, distance) {
    const bearingRad = bearing * Math.PI / 180
    const R = 6371000 // 地球半径
    
    const latOffset = (distance * Math.cos(bearingRad)) / R * 180 / Math.PI
    const lngOffset = (distance * Math.sin(bearingRad)) / R * 180 / Math.PI
    
    return { lat: latOffset, lng: lngOffset }
  }
  
  // 计算转弯半径
  calculateTurnRadius(segment, pointIndex) {
    if (pointIndex === 0 || pointIndex === segment.points.length - 1) {
      return Infinity // 起点和终点没有转弯
    }
    
    const prev = segment.points[pointIndex - 1]
    const curr = segment.points[pointIndex]
    const next = segment.points[pointIndex + 1]
    
    // 简化计算：使用三点确定的圆的半径
    const a = this.calculateDistance(prev.latitude, prev.longitude, curr.latitude, curr.longitude)
    const b = this.calculateDistance(curr.latitude, curr.longitude, next.latitude, next.longitude)
    const c = this.calculateDistance(prev.latitude, prev.longitude, next.latitude, next.longitude)
    
    const s = (a + b + c) / 2
    const area = Math.sqrt(s * (s - a) * (s - b) * (s - c))
    
    return (a * b * c) / (4 * area)
  }
  
  // 计算最大转弯速度
  calculateMaxTurnSpeed(turnRadius) {
    if (turnRadius === Infinity) {
      return this.options.maxSpeed
    }
    
    // 基于离心力限制的最大速度
    const maxCentripetalAcceleration = this.options.maxAcceleration * 0.5
    return Math.sqrt(maxCentripetalAcceleration * turnRadius)
  }
  
  // 获取地形高度
  getTerrainHeight(lat, lng) {
    const key = `${lat.toFixed(6)}_${lng.toFixed(6)}`
    
    if (this.terrainCache.has(key)) {
      return this.terrainCache.get(key)
    }
    
    // 模拟地形高度（实际应用中应该调用地形API）
    const height = Math.max(0, Math.sin(lat * 100) * Math.cos(lng * 100) * 50)
    this.terrainCache.set(key, height)
    
    return height
  }
  
  // 检查是否在禁飞区
  isInNoFlyZone(point) {
    return this.noFlyZones.some(zone => {
      const distance = this.calculateDistance(
        point.latitude, point.longitude,
        zone.latitude, zone.longitude
      )
      return distance <= zone.radius
    })
  }
  
  // 计算总距离
  calculateTotalDistance(segments) {
    return segments.reduce((total, segment) => total + segment.distance, 0)
  }
  
  // 计算预估时间
  calculateEstimatedTime(segments) {
    return segments.reduce((total, segment) => total + segment.estimatedTime, 0)
  }
  
  // 计算路径段时间
  calculateSegmentTime(points) {
    let totalTime = 0
    
    for (let i = 1; i < points.length; i++) {
      const prev = points[i - 1]
      const curr = points[i]
      
      const distance = this.calculateDistance(
        prev.latitude, prev.longitude,
        curr.latitude, curr.longitude
      )
      
      const avgSpeed = (prev.speed + curr.speed) / 2
      totalTime += distance / avgSpeed
    }
    
    return totalTime
  }
  
  // 添加安全检查点
  addSafetyCheckpoints(segments, options) {
    if (!options.addSafetyCheckpoints) {
      return segments
    }
    
    return segments.map(segment => {
      const safetyPoints = []
      
      segment.points.forEach((point, index) => {
        safetyPoints.push(point)
        
        // 在长距离段中添加安全检查点
        if (index < segment.points.length - 1) {
          const nextPoint = segment.points[index + 1]
          const distance = this.calculateDistance(
            point.latitude, point.longitude,
            nextPoint.latitude, nextPoint.longitude
          )
          
          if (distance > 100) { // 距离超过100米时添加检查点
            const midPoint = {
              latitude: (point.latitude + nextPoint.latitude) / 2,
              longitude: (point.longitude + nextPoint.longitude) / 2,
              altitude: (point.altitude + nextPoint.altitude) / 2,
              speed: (point.speed + nextPoint.speed) / 2,
              isSafetyCheckpoint: true
            }
            safetyPoints.push(midPoint)
          }
        }
      })
      
      return {
        ...segment,
        points: safetyPoints
      }
    })
  }
  
  // ==================== 公共API ====================
  
  // 设置禁飞区
  setNoFlyZones(zones) {
    this.noFlyZones = zones
  }
  
  // 设置障碍物
  setObstacles(obstacles) {
    this.obstacles = obstacles
  }
  
  // 清除缓存
  clearCache() {
    this.terrainCache.clear()
  }
  
  // 更新配置
  updateOptions(newOptions) {
    this.options = { ...this.options, ...newOptions }
  }
}

// 导出默认实例
export const pathPlanner = new PathPlanner()

// 导出工具函数
export const PathPlanningUtils = {
  // 计算航点间的最优顺序（旅行商问题的简化解法）
  optimizeWaypointOrder(waypoints, startPoint = null) {
    if (waypoints.length <= 2) return waypoints
    
    const unvisited = [...waypoints]
    const optimized = []
    
    // 选择起始点
    let current = startPoint || unvisited[0]
    if (startPoint) {
      const startIndex = unvisited.findIndex(wp => wp.id === startPoint.id)
      if (startIndex !== -1) {
        unvisited.splice(startIndex, 1)
      }
    } else {
      unvisited.shift()
    }
    
    optimized.push(current)
    
    // 贪心算法：每次选择最近的未访问点
    while (unvisited.length > 0) {
      let nearestIndex = 0
      let nearestDistance = current.distanceTo(unvisited[0])
      
      for (let i = 1; i < unvisited.length; i++) {
        const distance = current.distanceTo(unvisited[i])
        if (distance < nearestDistance) {
          nearestDistance = distance
          nearestIndex = i
        }
      }
      
      current = unvisited.splice(nearestIndex, 1)[0]
      optimized.push(current)
    }
    
    // 重新分配序列号
    optimized.forEach((waypoint, index) => {
      waypoint.sequence = index
    })
    
    return optimized
  },
  
  // 生成网格航点
  generateGridWaypoints(bounds, spacing, altitude, options = {}) {
    const waypoints = []
    let sequence = 0
    
    const { north, south, east, west } = bounds
    const latSpacing = spacing / 111000 // 大约每度111km
    const lngSpacing = spacing / (111000 * Math.cos(((north + south) / 2) * Math.PI / 180))
    
    for (let lat = south; lat <= north; lat += latSpacing) {
      for (let lng = west; lng <= east; lng += lngSpacing) {
        const waypoint = new Waypoint({
          latitude: lat,
          longitude: lng,
          altitude: altitude,
          sequence: sequence++,
          type: 'WAYPOINT',
          name: `Grid_${sequence}`,
          ...options
        })
        
        waypoints.push(waypoint)
      }
    }
    
    return waypoints
  },
  
  // 生成圆形航点
  generateCircularWaypoints(center, radius, numPoints, altitude, options = {}) {
    const waypoints = []
    const angleStep = (2 * Math.PI) / numPoints
    
    for (let i = 0; i < numPoints; i++) {
      const angle = i * angleStep
      const lat = center.latitude + (radius / 111000) * Math.cos(angle)
      const lng = center.longitude + (radius / (111000 * Math.cos(center.latitude * Math.PI / 180))) * Math.sin(angle)
      
      const waypoint = new Waypoint({
        latitude: lat,
        longitude: lng,
        altitude: altitude,
        sequence: i,
        type: 'WAYPOINT',
        name: `Circle_${i + 1}`,
        ...options
      })
      
      waypoints.push(waypoint)
    }
    
    return waypoints
  }
}
