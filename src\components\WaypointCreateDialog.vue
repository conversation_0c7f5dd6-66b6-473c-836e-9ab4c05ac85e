<template>
  <div class="dialog-overlay" @click="handleOverlayClick">
    <div class="waypoint-create-dialog" @click.stop>
      <div class="dialog-header">
        <h3>创建航点</h3>
        <button @click="$emit('close')" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="dialog-content">
        <form @submit.prevent="createWaypoint">
          <div class="form-grid">
            <!-- 基本信息 -->
            <div class="form-section">
              <h4>基本信息</h4>
              <div class="form-group">
                <label>名称</label>
                <input v-model="form.name" type="text" required />
              </div>
              
              <div class="form-group">
                <label>类型</label>
                <select v-model="form.type" required>
                  <option v-for="(info, type) in WAYPOINT_TYPES" :key="type" :value="type">
                    {{ info.name }}
                  </option>
                </select>
              </div>
            </div>
            
            <!-- 位置信息 -->
            <div class="form-section">
              <h4>位置信息</h4>
              <div class="form-row">
                <div class="form-group">
                  <label>纬度</label>
                  <input v-model.number="form.latitude" type="number" step="0.000001" required />
                </div>
                <div class="form-group">
                  <label>经度</label>
                  <input v-model.number="form.longitude" type="number" step="0.000001" required />
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label>海拔高度 (m)</label>
                  <input v-model.number="form.altitude" type="number" step="0.1" required />
                </div>
                <div class="form-group">
                  <label>相对高度 (m)</label>
                  <input v-model.number="form.relativeAltitude" type="number" step="0.1" />
                </div>
              </div>
            </div>
            
            <!-- 飞行参数 -->
            <div class="form-section">
              <h4>飞行参数</h4>
              <div class="form-row">
                <div class="form-group">
                  <label>速度 (m/s)</label>
                  <input v-model.number="form.speed" type="number" step="0.1" min="0" max="30" required />
                </div>
                <div class="form-group">
                  <label>航向角 (度)</label>
                  <input v-model.number="form.heading" type="number" step="1" min="0" max="360" />
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label>接受半径 (m)</label>
                  <input v-model.number="form.acceptanceRadius" type="number" step="0.1" min="0.5" max="50" />
                </div>
                <div class="form-group">
                  <label>停留时间 (秒)</label>
                  <input v-model.number="form.holdTime" type="number" step="1" min="0" />
                </div>
              </div>
            </div>
            
            <!-- 相机参数 -->
            <div class="form-section" v-if="showCameraParams">
              <h4>相机参数</h4>
              <div class="form-row">
                <div class="form-group">
                  <label>云台俯仰角 (度)</label>
                  <input v-model.number="form.gimbalPitch" type="number" step="1" min="-90" max="90" />
                </div>
                <div class="form-group">
                  <label>云台偏航角 (度)</label>
                  <input v-model.number="form.gimbalYaw" type="number" step="1" min="-180" max="180" />
                </div>
              </div>
              
              <div class="form-group">
                <label>相机动作</label>
                <select v-model="form.cameraAction">
                  <option value="NONE">无动作</option>
                  <option value="PHOTO">拍照</option>
                  <option value="VIDEO_START">开始录像</option>
                  <option value="VIDEO_STOP">停止录像</option>
                </select>
              </div>
            </div>
          </div>
          
          <!-- 预览信息 -->
          <div class="preview-section">
            <h4>预览</h4>
            <div class="preview-content">
              <div class="preview-item">
                <span class="label">坐标:</span>
                <span class="value">{{ formatCoordinates() }}</span>
              </div>
              <div class="preview-item">
                <span class="label">类型:</span>
                <span class="value" :style="{ color: getTypeInfo().color }">
                  <i :class="getTypeInfo().icon"></i>
                  {{ getTypeInfo().name }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="dialog-actions">
            <button type="button" @click="$emit('close')" class="btn-cancel">
              取消
            </button>
            <button type="submit" class="btn-create" :disabled="!isFormValid">
              创建航点
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useWaypointStore } from '@/stores/WaypointStore.js'
import { WAYPOINT_TYPES } from '@/models/WaypointModels.js'

export default {
  name: 'WaypointCreateDialog',
  emits: ['close', 'created'],
  setup(props, { emit }) {
    const waypointStore = useWaypointStore()
    
    // 表单数据
    const form = ref({
      name: '',
      type: 'WAYPOINT',
      latitude: 39.9042,
      longitude: 116.4074,
      altitude: 50,
      relativeAltitude: 50,
      speed: 5.0,
      heading: 0,
      acceptanceRadius: 2.0,
      holdTime: 0,
      gimbalPitch: 0,
      gimbalYaw: 0,
      cameraAction: 'NONE'
    })
    
    // 计算属性
    const showCameraParams = computed(() => {
      return ['PHOTO', 'VIDEO_START', 'VIDEO_STOP'].includes(form.value.type)
    })
    
    const isFormValid = computed(() => {
      return form.value.name.trim() !== '' &&
             form.value.latitude >= -90 && form.value.latitude <= 90 &&
             form.value.longitude >= -180 && form.value.longitude <= 180 &&
             form.value.altitude >= 0 &&
             form.value.speed > 0
    })
    
    // 方法
    function getTypeInfo() {
      return WAYPOINT_TYPES[form.value.type] || WAYPOINT_TYPES.WAYPOINT
    }
    
    function formatCoordinates() {
      return `${form.value.latitude.toFixed(6)}, ${form.value.longitude.toFixed(6)}`
    }
    
    function generateDefaultName() {
      const typeInfo = getTypeInfo()
      const timestamp = Date.now().toString().slice(-4)
      form.value.name = `${typeInfo.name}_${timestamp}`
    }
    
    function createWaypoint() {
      try {
        const waypoint = waypointStore.createWaypoint(form.value)
        emit('created', waypoint)
      } catch (error) {
        alert(`创建航点失败: ${error.message}`)
      }
    }
    
    function handleOverlayClick() {
      emit('close')
    }
    
    // 生命周期
    onMounted(() => {
      generateDefaultName()
    })
    
    return {
      form,
      showCameraParams,
      isFormValid,
      WAYPOINT_TYPES,
      getTypeInfo,
      formatCoordinates,
      generateDefaultName,
      createWaypoint,
      handleOverlayClick
    }
  }
}
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
  backdrop-filter: blur(5px);
}

.waypoint-create-dialog {
  background: rgba(15, 25, 40, 0.95);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  background: rgba(0, 255, 255, 0.05);
}

.dialog-header h3 {
  margin: 0;
  color: #00ffff;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-size: 16px;
}

.close-btn:hover {
  color: #ff6b47;
  background: rgba(255, 107, 71, 0.1);
}

.dialog-content {
  padding: 24px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.form-section {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
}

.form-section h4 {
  margin: 0 0 16px 0;
  color: #00ffff;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  padding-bottom: 8px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: #ffffff;
  font-size: 13px;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #00ffff;
  box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.2);
}

.preview-section {
  background: rgba(0, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.preview-section h4 {
  margin: 0 0 12px 0;
  color: #00ffff;
  font-size: 14px;
  font-weight: 600;
}

.preview-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.preview-item .label {
  color: rgba(255, 255, 255, 0.7);
}

.preview-item .value {
  color: #ffffff;
  font-weight: 600;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-cancel,
.btn-create {
  padding: 10px 20px;
  border: 1px solid;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-cancel {
  background: rgba(149, 165, 166, 0.1);
  border-color: rgba(149, 165, 166, 0.5);
  color: #95a5a6;
}

.btn-cancel:hover {
  background: rgba(149, 165, 166, 0.2);
  border-color: rgba(149, 165, 166, 0.7);
}

.btn-create {
  background: rgba(46, 204, 113, 0.1);
  border-color: rgba(46, 204, 113, 0.5);
  color: #2ecc71;
}

.btn-create:hover:not(:disabled) {
  background: rgba(46, 204, 113, 0.2);
  border-color: rgba(46, 204, 113, 0.7);
}

.btn-create:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .preview-content {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
}
</style>
