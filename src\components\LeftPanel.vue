<template>
  <div class="left-panel">
    <!-- 悬浮按钮 -->
    <div class="floating-buttons">
      <div
        class="floating-btn"
        :class="{ active: selectedTabs.includes('attitude') }"
        @click="toggleTab('attitude')"
        title="飞行姿态">
        <i class="fas fa-compass"></i>
        <span>姿态</span>
      </div>
      <div
        class="floating-btn"
        :class="{ active: selectedTabs.includes('waypoints') }"
        @click="toggleTab('waypoints')"
        title="航点管理">
        <i class="fas fa-map-marker-alt"></i>
        <span>航点</span>
      </div>
    </div>

    <!-- 悬浮面板 -->
    <div class="floating-panels">
      <!-- 姿态面板 -->
      <div v-show="selectedTabs.includes('attitude')" class="floating-panel attitude-panel">
        <div class="panel-header">
          <i class="fas fa-compass"></i>
          <div class="panel-title">姿态</div>
          <i class="fas fa-times close-btn" @click="removeTab('attitude')"></i>
        </div>
        <div class="panel-content">
          <!-- 重新设计的仪表盘布局 -->
          <div class="dashboard-grid">
            <!-- 主仪表区域 -->
            <div class="main-instruments">
              <NewAttitudeIndicator 
                :pitch="flightData.pitch"
                :roll="flightData.roll"
                :size="180"
              />
              <CyberCompass 
                :heading="flightData.yaw"
                :size="160"
                class="cyber-compass"
              />
            </div>
            
            <!-- 关键指标 -->
            <div class="key-metrics">
              <div class="metric-card speed">
                <div class="metric-header">
                  <i class="fas fa-tachometer-alt"></i>
                  <span>速度</span>
                </div>
                <div class="metric-value">{{ flightData.airSpeed.toFixed(1) }}</div>
                <div class="metric-unit">m/s</div>
                <div class="metric-details">
                  <span>地速: {{ flightData.groundSpeed.toFixed(1) }}</span>
                  <span>垂直: {{ flightData.verticalSpeed.toFixed(1) }}</span>
                </div>
              </div>
              <div class="metric-card altitude">
                <div class="metric-header">
                  <i class="fas fa-mountain"></i>
                  <span>高度</span>
                </div>
                <div class="metric-value">{{ flightData.relativeAltitude.toFixed(0) }}</div>
                <div class="metric-unit">m</div>
                <div class="metric-details">
                  <span>海拔: {{ flightData.altitude.toFixed(0) }}</span>
                  <span>气压: 1013</span>
                </div>
              </div>
            </div>

            <!-- 辅助信息 -->
            <div class="aux-info">
              <div class="info-group">
                <div class="info-title"><i class="fas fa-satellite-dish"></i> 状态</div>
                <div class="info-item"><span>飞行模式</span> <span class="value mode">{{ flightModes[flightData.flightMode] || flightData.flightMode }}</span></div>
                <div class="info-item"><span>GPS状态</span> <span class="value" :class="getGpsStatusClass()">{{ gpsStatusMap[flightData.gpsStatus] || flightData.gpsStatus }}</span></div>
              </div>
              <div class="info-group">
                <div class="info-title"><i class="fas fa-broadcast-tower"></i> 导航</div>
                <div class="info-item"><span>卫星</span> <span class="value">{{ flightData.satelliteCount }}</span></div>
                <div class="info-item"><span>HDOP</span> <span class="value">{{ flightData.hdop.toFixed(2) }}</span></div>
              </div>
              <div class="info-group">
                <div class="info-title"><i class="fas fa-cogs"></i> 系统</div>
                <div class="info-item"><span>电池</span> <span class="value" :class="getBatteryClass()">{{ flightData.batteryPercent }}% ({{ flightData.batteryVoltage.toFixed(1) }}V)</span></div>
                <div class="info-item"><span>信号</span> <span class="value" :class="getSignalClass()">{{ flightData.signalStrength }} dBm</span></div>
              </div>
              <div class="info-group">
                <div class="info-title"><i class="fas fa-wind"></i> 风速</div>
                <div class="info-item"><span>风速</span> <span class="value">{{ flightData.windSpeed.toFixed(1) }} m/s</span></div>
                <div class="info-item"><span>风向</span> <span class="value">{{ Math.round(flightData.windDirection) }}°</span></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 航点面板 -->
      <div v-show="selectedTabs.includes('waypoints')" class="floating-panel waypoints-panel">
        <div class="panel-header">
          <i class="fas fa-map-marker-alt"></i>
          <div class="panel-title">航点管理</div>
          <i class="fas fa-times close-btn" @click="removeTab('waypoints')"></i>
        </div>
        <div class="panel-content">
          <WaypointPanel
            @waypoint-selected="handleWaypointSelected"
            @waypoint-located="handleWaypointLocated"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import NewAttitudeIndicator from './CyberAttitudeIndicator.vue'
import CyberCompass from './CyberCompass.vue'
import WaypointPanel from './WaypointPanel.vue'

export default {
  name: 'LeftPanel',
  components: {
    NewAttitudeIndicator,
    CyberCompass,
    WaypointPanel
  },
  emits: ['waypoint-selected', 'waypoint-located'],
  data() {
    return {
      // 选项卡配置
      selectedTabs: [], // 默认不显示，需要点击按钮
      
      // 静态飞行数据 - 不进行动态更新
      flightData: {
        // 姿态数据 (度) - 静态测试值
        pitch: -5.2,          // 俯仰角 (向下5.2度)
        roll: 12.8,           // 横滚角 (向右12.8度)
        yaw: 126.0,           // 偏航角 (东南方向126度)
        
        // 速度数据 - 静态测试值
        groundSpeed: 15.3,    // 地速 (m/s)
        airSpeed: 16.1,       // 空速 (m/s)
        verticalSpeed: 2.1,   // 垂直速度 (m/s) - 上升
        
        // 位置数据 - 静态测试值
        altitude: 145.6,      // 海拔高度 (m)
        relativeAltitude: 25.1, // 相对起飞点高度 (m)
        
        // 导航数据 - 静态测试值
        gpsStatus: 'RTK_FIXED', // GPS状态
        satelliteCount: 18,     // 卫星数量
        hdop: 0.6,             // 水平精度因子
        
        // 系统状态 - 静态测试值
        flightMode: 'AUTO',     // 飞行模式
        batteryVoltage: 23.1,   // 电池电压 (V)
        batteryPercent: 92,     // 电池百分比
        signalStrength: -58,    // 信号强度 (dBm)
        
        // 风速数据 - 静态测试值
        windSpeed: 4.2,         // 风速 (m/s)
        windDirection: 280      // 风向 (度) - 西北风
      },
      
      // 映射表
      flightModes: {
        'MANUAL': '手动',
        'AUTO': '自动',
        'GUIDED': '引导',
        'LOITER': '悬停',
        'RTL': '返航',
        'LAND': '降落'
      },
      
      gpsStatusMap: {
        'NO_GPS': '无GPS',
        'NO_FIX': '无定位',
        'GPS_FIX': 'GPS定位',
        'DGPS_FIX': 'DGPS定位',
        'RTK_FLOAT': 'RTK浮点',
        'RTK_FIXED': 'RTK固定'
      }
    }
  },
  mounted() {
    this.startDataUpdates();
    // this.startAnimations() // 如果有纯视觉动画，也在这里启动
  },
  beforeUnmount() {
    if (this.dataUpdateInterval) {
      clearInterval(this.dataUpdateInterval);
    }
  },
  methods: {
    // 启动模拟数据更新
    startDataUpdates() {
      this.dataUpdateInterval = setInterval(() => {
        // 模拟飞行姿态的平滑随机变化
        this.flightData.pitch += (Math.random() - 0.5) * 1.5;
        this.flightData.roll += (Math.random() - 0.5) * 2.0;

        // 保持在合理范围内
        this.flightData.pitch = Math.max(-45, Math.min(45, this.flightData.pitch));
        this.flightData.roll = Math.max(-60, Math.min(60, this.flightData.roll));

        // 模拟航向的平滑变化
        this.flightData.yaw += (Math.random() - 0.4) * 2.0;
        if (this.flightData.yaw > 360) this.flightData.yaw -= 360;
        if (this.flightData.yaw < 0) this.flightData.yaw += 360;

        // 模拟其他数据的微小波动
        this.flightData.airSpeed += (Math.random() - 0.5) * 0.2;
        this.flightData.relativeAltitude += (Math.random() - 0.5) * 0.5;

      }, 100); // 每100毫秒更新一次
    },

    // 选项卡管理方法
    toggleTab(tabId) {
      const index = this.selectedTabs.indexOf(tabId)
      if (index === -1) {
        this.selectedTabs.push(tabId)
      } else {
        this.selectedTabs.splice(index, 1)
      }
    },
    
    removeTab(tabId) {
      const index = this.selectedTabs.indexOf(tabId)
      if (index > -1) {
        this.selectedTabs.splice(index, 1)
      }
    },
    
    // 状态样式类方法
    getGpsStatusClass() {
      switch (this.flightData.gpsStatus) {
        case 'RTK_FIXED': return 'status-excellent'
        case 'RTK_FLOAT': return 'status-good'
        case 'DGPS_FIX': return 'status-normal'
        case 'GPS_FIX': return 'status-normal'
        default: return 'status-poor'
      }
    },
    
    getVerticalSpeedClass() {
      if (this.flightData.verticalSpeed > 0.5) return 'climb'
      if (this.flightData.verticalSpeed < -0.5) return 'descend'
      return 'hover'
    },
    
    getBatteryClass() {
      if (this.flightData.batteryPercent < 20) return 'battery-critical'
      if (this.flightData.batteryPercent < 30) return 'battery-low'
      if (this.flightData.batteryPercent > 80) return 'battery-good'
      return 'battery-normal'
    },
    
    getSignalClass() {
      if (this.flightData.signalStrength > -50) return 'signal-excellent'
      if (this.flightData.signalStrength > -70) return 'signal-good'
      if (this.flightData.signalStrength > -80) return 'signal-fair'
      return 'signal-poor'
    },

    // 航点相关事件处理
    handleWaypointSelected(waypoint) {
      console.log('左侧面板接收到航点选中事件:', waypoint)
      // 可以在这里添加与其他组件的联动逻辑
      this.$emit('waypoint-selected', waypoint)
    },

    handleWaypointLocated(waypoint) {
      console.log('左侧面板接收到航点定位事件:', waypoint)
      // 可以在这里添加地图定位逻辑
      this.$emit('waypoint-located', waypoint)
    }
  }
}
</script>

<style scoped>
/* ================================ 左侧悬浮面板 ================================ */
.left-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  pointer-events: none;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

/* 左侧悬浮按钮组 */
.floating-buttons {
  position: fixed;
  top: 50%;
  left: 30px;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 12px;
  pointer-events: auto;
  z-index: 1001;
  align-items: center;
}

.floating-btn {
  padding: 12px;
  background: rgba(0, 0, 0, 0.7); /* 与右下角按钮一致的背景 */
  border: 1px solid rgba(255, 255, 255, 0.1); /* 简洁的边框 */
  border-radius: 8px; /* 与右下角按钮一致的圆角 */
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: all 0.2s ease; /* 更快的过渡 */
  color: rgba(255, 255, 255, 0.8);
  font-size: 10px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  width: 60px;
  height: 60px;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3); /* 简化阴影 */
}

.floating-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 1);
  transform: translateY(-1px); /* 轻微的悬浮效果 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.floating-btn.active {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.floating-btn i {
  font-size: 18px;
  color: inherit;
}

/* 悬浮内容面板 */
.floating-panels {
  position: fixed;
  top: 100px;
  left: 110px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  pointer-events: auto;
  z-index: 1000;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
}

.floating-panel {
  width: 400px;
  background: rgba(0, 0, 0, 0.85); /* 与按钮一致的背景透明度 */
  border: 1px solid rgba(255, 255, 255, 0.15); /* 简化边框 */
  border-radius: 12px;
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4); /* 简化阴影 */
  animation: slideInLeft 0.3s ease;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; /* 现代字体 */
}

/* 航点面板特殊样式 */
.waypoints-panel {
  width: 450px;
  height: 650px;
}

.waypoints-panel .panel-content {
  height: calc(100% - 60px);
  overflow: hidden;
}





.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.05); /* 轻微的分隔 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.panel-header i {
  font-size: 16px;
  margin-right: 8px;
  color: rgba(255, 255, 255, 0.7);
}

.panel-title {
  flex: 1;
  font-weight: 600;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.close-btn {
  cursor: pointer;
  color: rgba(255, 255, 255, 0.5);
  transition: all 0.2s ease;
  font-size: 14px;
  padding: 4px;
  border-radius: 4px;
}

.close-btn:hover {
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.1);
}

.panel-content {
  padding: 15px;
  background: rgba(10, 20, 30, 0.5);
}

/* 新的仪表盘网格布局 */
.dashboard-grid {
  display: grid;
  grid-template-areas:
    "main-instruments"
    "key-metrics"
    "aux-info";
  grid-template-rows: auto auto 1fr;
  gap: 15px;
  color: #e0e0e0;
}

.main-instruments {
  grid-area: main-instruments;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: rgba(0, 0, 0, 0.2);
  padding: 10px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.key-metrics {
  grid-area: key-metrics;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.metric-card {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 15px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-3px);
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 255, 255, 0.5);
}

.metric-header {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 10px;
}

.metric-header i {
  margin-right: 5px;
}

.metric-value {
  font-size: 36px;
  font-weight: 700;
  color: #fff;
  line-height: 1;
}

.metric-unit {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 10px;
}

.metric-details {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  display: flex;
  justify-content: space-around;
}

.aux-info {
  grid-area: aux-info;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.info-group {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.info-title {
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 5px;
}

.info-title i {
  margin-right: 8px;
  color: #00ffff;
}

.info-item {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  padding: 5px 0;
}

.info-item .value {
  font-weight: 600;
  color: #fff;
}

.info-item .value.mode {
  color: #ffde59;
}

/* 移除旧样式 */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 旧的 .flight-instruments 等相关样式可以被移除或注释掉，因为我们采用了新的 .dashboard-grid 布局 */
.flight-instruments, .instrument-row, .cyber-display, .flight-status, .flight-readouts {
  display: none;
}


/* 专业航空仪表样式 */
/* .flight-instruments {
  padding: 20px;
} */

/* .instrument-row {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 20px;
  gap: 15px;
} */

/* .instrument-row.primary {
  margin-bottom: 25px;
} */

/* .instrument-row.secondary {
  margin-bottom: 20px;
} */

/* .cyber-indicator {
  flex: 1;
  display: flex;
  justify-content: center;
} */

/* .cyber-compass {
  flex: 1;
  display: flex;
  justify-content: center;
} */

/* 现代简约显示面板 */
/* .cyber-display {
  flex: 1;
  background: rgba(255, 255, 255, 0.05); /* 轻微的白色背景 */
/* border: 1px solid rgba(255, 255, 255, 0.1); */
/* border-radius: 8px; */
/* padding: 16px; */
/* position: relative; */
/* overflow: hidden; */
/* } */

/* .cyber-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(0, 255, 255, 0.8), 
    transparent);
  /* 暂时禁用动画 */
  /* animation: data-flow 2s linear infinite; */
/* } */

/* .display-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
} */

/* .display-header i {
  font-size: 14px;
} */

/* .display-content {
  text-align: center;
} */

/* .primary-value {
  font-size: 28px; /* 稍微减小 */
/* font-weight: 700; */
/* color: rgba(255, 255, 255, 0.95); */
/* text-shadow: none; /* 移除发光效果 */
/* line-height: 1; */
/* margin-bottom: 4px; */
/* } */

/* .unit {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8px;
  font-weight: 500;
} */

/* .secondary-info {
  display: flex;
  justify-content: space-between;
  gap: 8px;
} */

/* .info-item {
  flex: 1;
  text-align: center;
  padding: 6px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.05);
} */

/* .info-item .label {
  display: block;
  font-size: 9px;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 2px;
  font-weight: 500;
} */

/* .info-item .value {
  display: block;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
} */

/* 飞行状态指示 */
/* .flight-status {
  margin-bottom: 20px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.08);
} */

/* .status-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
} */

/* .status-item {
  text-align: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.05);
} */

/* .status-label {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 4px;
  font-weight: 500;
} */

/* .status-value {
  font-size: 12px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
} */

/* .status-value.mode {
  color: rgba(255, 255, 255, 0.9);
} */

/* 数据读数区域 */
/* .flight-readouts {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
} */

/* .readout-section {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 6px;
  padding: 12px;
  position: relative;
} */

/* .section-title {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 10px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 10px;
  font-weight: 600;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 6px;
} */

/* .section-title i {
  font-size: 11px;
} */

/* .readout-grid {
  display: grid;
  gap: 8px;
} */

/* .readout-item {
  text-align: center;
  padding: 6px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.03);
} */

/* .readout-label {
  font-size: 8px;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 2px;
  font-weight: 500;
} */

/* .readout-value {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
} */

/* 状态颜色 - 简约风格 */
/* .climb { 
  color: #4ade80 !important; /* 现代绿色 */
/* } */
/* .descend { 
  color: #f87171 !important; /* 现代红色 */
/* } */
/* .hover { 
  color: #fbbf24 !important; /* 现代黄色 */
/* } */

/* .status-excellent { 
  color: #4ade80 !important; /* 绿色 */
/* } */
/* .status-good { 
  color: #a3e635 !important; /* 浅绿色 */
/* } */
/* .status-normal { 
  color: #fbbf24 !important; /* 黄色 */
/* } */
/* .status-poor { 
  color: #f87171 !important; /* 红色 */
/* } */

/* .battery-good { 
  color: #4ade80 !important; 
} */
/* .battery-normal { 
  color: rgba(255, 255, 255, 0.8) !important; 
} */
/* .battery-low { 
  color: #fbbf24 !important; 
} */
/* .battery-critical { 
  color: #f87171 !important; 
  animation: blink 1s infinite;
} */

/* .signal-excellent { 
  color: #4ade80 !important; 
} */
/* .signal-good { 
  color: #a3e635 !important; 
} */
/* .signal-fair { 
  color: #fbbf24 !important; 
} */
/* .signal-poor { 
  color: #f87171 !important; 
} */

/* @keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.6; }
} */

/* 响应式设计 */
@media (max-width: 768px) {
    .floating-buttons {
        left: 10px;
    }

    .floating-panels {
        left: 80px;
    }

    .floating-panel {
        width: calc(100vw - 120px);
        max-width: 380px;
    }

    .floating-btn {
        width: 50px;
        height: 50px;
        padding: 10px 6px;
    }

    .floating-btn i {
        font-size: 16px;
    }

    /* .flight-readouts {
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    } */

    /* .instrument-row {
        flex-direction: column;
        gap: 10px;
    } */

    /* .primary-value {
        font-size: 24px;
    } */
}

@media (max-width: 480px) {
    .floating-panels {
        left: 70px;
    }

    .floating-panel {
        width: calc(100vw - 100px);
        max-width: 320px;
    }

    .floating-btn {
        width: 45px;
        height: 45px;
        padding: 8px 4px;
    }

    .floating-btn i {
        font-size: 14px;
    }

    /* .flight-readouts {
        grid-template-columns: 1fr;
        gap: 6px;
    } */

    /* .status-row {
        grid-template-columns: 1fr;
        gap: 10px;
    } */

    /* .instrument-row {
        gap: 8px;
    } */

    /* .primary-value {
        font-size: 22px;
    } */

    /* .readout-value {
        font-size: 10px;
    } */
}
</style>
