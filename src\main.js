import { createApp } from 'vue'
import App from './App.vue'
import './style.css'

// 导入 Leaflet CSS
import 'leaflet/dist/leaflet.css'

// 导入航点存储系统
import { waypointStore } from '@/stores/WaypointStore.js'

// 导入演示数据工具
import { initializeDemoData } from '@/utils/demoData.js'

const app = createApp(App)

// 全局提供航点存储
app.provide('waypointStore', waypointStore)

app.mount('#app')

// 开发环境下的调试信息和演示数据初始化
if (process.env.NODE_ENV === 'development') {
  console.log('🚁 无人机控制系统已启动')
  console.log('📍 航点管理系统已初始化')

  // 将航点存储暴露到全局，方便调试
  window.waypointStore = waypointStore

  // 初始化演示数据
  setTimeout(() => {
    try {
      initializeDemoData()
    } catch (error) {
      console.error('演示数据初始化失败:', error)
    }
  }, 1000) // 延迟1秒确保所有组件都已加载
}
