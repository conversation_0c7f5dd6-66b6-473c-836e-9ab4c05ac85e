<template>
  <div class="main-container">
    <!-- 左侧任务控制面板 -->
    <LeftPanel />

    <!-- 中央地图区域 -->
    <MapContainer />

    <!-- 右侧视频和控制面板 -->
    <RightPanel />
  </div>
</template>

<script>
import LeftPanel from './components/LeftPanel.vue'
import MapContainer from './components/MapContainer.vue'
import RightPanel from './components/RightPanel.vue'

export default {
  name: 'App',
  components: {
    LeftPanel,
    MapContainer,
    RightPanel
  }
}
</script>

<style scoped>
.main-container {
    display: flex;
    height: 100vh;
    background: 
        radial-gradient(circle at 20% 80%, rgba(126, 211, 33, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(65, 105, 225, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, #0a0f1a 0%, #1a2332 100%);
}
</style>
