<template>
  <div class="main-container">
    <!-- 左侧任务控制面板 -->
    <LeftPanel
      @waypoint-selected="handleWaypointSelected"
      @waypoint-located="handleWaypointLocated"
    />

    <!-- 中央地图区域 -->
    <MapContainer
      ref="mapContainer"
      @waypoint-selected="handleWaypointSelected"
      @waypoint-located="handleWaypointLocated"
    />

    <!-- 右侧视频和控制面板 -->
    <RightPanel />
  </div>
</template>

<script>
import LeftPanel from './components/LeftPanel.vue'
import MapContainer from './components/MapContainer.vue'
import RightPanel from './components/RightPanel.vue'

export default {
  name: 'App',
  components: {
    LeftPanel,
    MapContainer,
    RightPanel
  },
  methods: {
    handleWaypointSelected(waypoint) {
      console.log('App: 航点被选中', waypoint)
      // 可以在这里添加全局的航点选中处理逻辑
    },

    handleWaypointLocated(waypoint) {
      console.log('App: 定位到航点', waypoint)
      // 通知地图组件定位到航点
      if (this.$refs.mapContainer) {
        this.$refs.mapContainer.handleWaypointLocated(waypoint)
      }
    }
  }
}
</script>

<style scoped>
.main-container {
    display: flex;
    height: 100vh;
    background: 
        radial-gradient(circle at 20% 80%, rgba(126, 211, 33, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(65, 105, 225, 0.1) 0%, transparent 50%),
        linear-gradient(135deg, #0a0f1a 0%, #1a2332 100%);
}
</style>
