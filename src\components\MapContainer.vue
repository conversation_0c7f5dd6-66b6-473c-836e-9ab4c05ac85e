<template>
  <div class="map-container">
    <!-- 真实地图显示 -->
    <div id="realMap" ref="mapContainer"></div>

    <div class="map-overlay"></div>

    <!-- 飞行状态HUD -->
    <div class="flight-hud">
      <div class="hud-left">
        <div class="drone-info">
          <div class="drone-id">UAV-001</div>
          <div class="flight-mode">AUTO</div>
        </div>
        <div class="connection-status">
          <div class="signal-indicator">
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar"></div>
            <div class="signal-bar active"></div>
          </div>
          <span class="signal-text">LINK</span>
        </div>
      </div>

      <div class="hud-center">
        <div class="mission-time">{{ currentTime }}</div>
        <div class="coordinates">39°54'15"N 116°24'27"E</div>
      </div>

      <div class="hud-right">
        <div class="telemetry-item">
          <span class="telem-label">ALT</span>
          <span class="telem-value">120m</span>
        </div>
        <div class="telemetry-item">
          <span class="telem-label">SPD</span>
          <span class="telem-value">15m/s</span>
        </div>
        <div class="telemetry-item">
          <span class="telem-label">BAT</span>
          <span class="telem-value">87%</span>
        </div>
        <div class="telemetry-item">
          <span class="telem-label">RTK</span>
          <span class="telem-value rtk-fix">FIX</span>
        </div>
      </div>
    </div>

    <!-- 无人机标记 (HTML覆盖层) -->
    <div
      class="drone-marker"
      :style="{ top: dronePosition.top, left: dronePosition.left }"
    >
      <div class="drone-radar-ring"></div>
      <div class="drone-radar-pulse"></div>
      <div class="drone-icon-container">
        <div class="drone-body">
          <div class="drone-center">
            <div class="drone-led"></div>
          </div>
          <div class="drone-arm drone-arm-1">
            <div class="drone-propeller"></div>
          </div>
          <div class="drone-arm drone-arm-2">
            <div class="drone-propeller"></div>
          </div>
          <div class="drone-arm drone-arm-3">
            <div class="drone-propeller"></div>
          </div>
          <div class="drone-arm drone-arm-4">
            <div class="drone-propeller"></div>
          </div>
        </div>
        <div class="drone-label">UAV-001</div>
        <div class="drone-status">在线</div>
      </div>
    </div>



    <!-- 无人机操控按钮组 -->
    <div class="drone-controls">
      <div
        v-for="control in droneControls"
        :key="control.name"
        class="drone-control-btn"
        :class="{
          active: control.active,
          emergency: control.emergency
        }"
        :title="control.name"
        @click="handleDroneControl(control)"
      >
        <i :class="control.icon"></i>
        <span class="btn-label">{{ control.name }}</span>
      </div>
    </div>

    <!-- 地图控制按钮 -->
    <div class="map-controls">
      <div class="map-control-btn" title="放大" @click="zoomIn">
        <i class="fas fa-plus"></i>
      </div>
      <div class="map-control-btn" title="缩小" @click="zoomOut">
        <i class="fas fa-minus"></i>
      </div>
      <div class="map-control-btn" title="定位" @click="centerMap">
        <i class="fas fa-crosshairs"></i>
      </div>
      <div class="map-control-btn" title="图层" @click="toggleLayer">
        <i class="fas fa-layer-group"></i>
      </div>
    </div>



    <!-- 状态提示 -->
    <div v-if="statusMessage" class="status-toast">
      {{ statusMessage }}
    </div>

    <!-- 航点地图图层 -->
    <WaypointMapLayer
      v-if="map"
      :map="map"
      :show-performance-stats="false"
      @waypoint-selected="handleWaypointSelected"
      @waypoint-located="handleWaypointLocated"
    />
  </div>
</template>

<script>
import { MAP_CONFIG, createMapTypeGroup, getAvailableMapTypes } from '@/utils/mapConfig'
import WaypointMapLayer from './WaypointMapLayer.vue'
import L from 'leaflet'

export default {
  name: 'MapContainer',
  components: {
    WaypointMapLayer
  },
  emits: ['waypoint-selected', 'waypoint-located'],
  data() {
    return {
      map: null,
      droneMarker: null,

      dronePosition: {
        top: '45%',
        left: '45%'
      },
      currentTime: '',
      currentMapType: '矢量地图',
      bandwidth: 58,
      currentMapLayer: 'vector',
      availableMapTypes: getAvailableMapTypes(),
      mapLayers: {},
      statusMessage: '',
      droneControls: [
        { name: '起飞', icon: 'fas fa-rocket', active: false, emergency: false },
        { name: '降落', icon: 'fas fa-landmark', active: false, emergency: false },
        { name: '悬停', icon: 'fas fa-pause-circle', active: false, emergency: false },
        { name: '返航', icon: 'fas fa-undo-alt', active: false, emergency: false },
        { name: '停止', icon: 'fas fa-hand-paper', active: false, emergency: true },
        { name: '锁定', icon: 'fas fa-lock', active: false, emergency: false }
      ],
      currentMode: null
    }
  },
  mounted() {
    this.updateTime()
    setInterval(this.updateTime, 1000)

    // 延迟初始化地图，确保DOM已经渲染
    this.$nextTick(() => {
      setTimeout(() => {
        this.initMap()

      }, 500) // 增加延迟时间
    })
  },
  methods: {
    initMap() {
      try {
        // 检查DOM元素是否存在
        const mapElement = document.getElementById('realMap')
        if (!mapElement) {
          console.error('地图容器元素未找到')
          return
        }

        // 初始化地图
        this.map = L.map('realMap', {
          center: [39.9042, 116.4074], // 北京天安门
          zoom: 13,
          zoomControl: false
        })

        // 创建所有地图图层
        this.availableMapTypes.forEach(mapType => {
          this.mapLayers[mapType.id] = createMapTypeGroup(mapType.id, MAP_CONFIG.TIANDITU_API_KEY)
        })

        // 添加默认图层
        this.map.addLayer(this.mapLayers[this.currentMapLayer])
      } catch (error) {
        console.error('地图初始化失败:', error)
      }
    },

    updateTime() {
      const now = new Date()
      this.currentTime = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },



    handleDroneControl(control) {
      // 重置所有控制状态
      this.droneControls.forEach(ctrl => {
        ctrl.active = false
      })

      // 激活当前控制
      control.active = true
      this.currentMode = control.name

      // 显示状态消息
      this.statusMessage = `执行${control.name}操作...`
      setTimeout(() => {
        this.statusMessage = ''
        control.active = false
        this.currentMode = null
      }, 2000)
    },

    zoomIn() {
      this.map.zoomIn()
    },

    zoomOut() {
      this.map.zoomOut()
    },

    centerMap() {
      this.map.setView([39.9042, 116.4074], 13)
    },

    toggleLayer() {
      // 获取当前地图类型的索引
      const currentIndex = this.availableMapTypes.findIndex(type => type.id === this.currentMapLayer)
      const nextIndex = (currentIndex + 1) % this.availableMapTypes.length
      const nextMapType = this.availableMapTypes[nextIndex]

      if (nextMapType && this.mapLayers[nextMapType.id]) {
        // 移除当前图层
        this.map.removeLayer(this.mapLayers[this.currentMapLayer])

        // 添加新图层
        this.map.addLayer(this.mapLayers[nextMapType.id])

        // 更新状态
        this.currentMapLayer = nextMapType.id
        this.currentMapType = nextMapType.name

        // 可选：显示切换提示
        console.log(`地图已切换到: ${nextMapType.name} - ${nextMapType.description}`)
      }
    },

    // 航点相关事件处理
    handleWaypointSelected(waypoint) {
      console.log('选中航点:', waypoint)
      // 可以在这里添加航点选中后的处理逻辑
    },

    handleWaypointLocated(waypoint) {
      // 定位到航点位置
      this.map.setView([waypoint.latitude, waypoint.longitude], 16)
      this.statusMessage = `已定位到航点: ${waypoint.name}`
      setTimeout(() => {
        this.statusMessage = ''
      }, 2000)
    }
  }
}
</script>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #0f1928;
}

#realMap {
  width: 100%;
  height: 100vh;
  z-index: 1;
  position: relative;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(15, 25, 40, 0.1) 0%,
    rgba(15, 25, 40, 0.05) 50%,
    rgba(15, 25, 40, 0.1) 100%
  );
  pointer-events: none;
  z-index: 2;
}

/* 飞行状态HUD */
.flight-hud {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: auto;
  max-width: 1200px;
  min-width: 800px;
  height: 50px;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 25px;
  z-index: 1000;
  font-family: 'Courier New', monospace;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.hud-left {
  display: flex;
  align-items: center;
  gap: 30px;
}

.drone-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.drone-id {
  color: #ff4500;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(255, 69, 0, 0.5);
}

.flight-mode {
  color: #00ff00;
  font-size: 10px;
  font-weight: bold;
  background: rgba(0, 255, 0, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid rgba(0, 255, 0, 0.3);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.signal-indicator {
  display: flex;
  align-items: end;
  gap: 2px;
  height: 16px;
}

.signal-bar {
  width: 3px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 1px;
}

.signal-bar:nth-child(1) { height: 4px; }
.signal-bar:nth-child(2) { height: 8px; }
.signal-bar:nth-child(3) { height: 12px; }
.signal-bar:nth-child(4) { height: 16px; }

.signal-bar.active {
  background: #00ff00;
  box-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
}

.signal-text {
  color: #00ff00;
  font-size: 10px;
  font-weight: bold;
}

.hud-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.mission-time {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.coordinates {
  color: rgba(255, 255, 255, 0.7);
  font-size: 11px;
  font-weight: normal;
}

.hud-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.telemetry-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.telem-label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 9px;
  font-weight: bold;
}

.telem-value {
  color: #ffffff;
  font-size: 14px;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

.rtk-fix {
  color: #00ff00;
  text-shadow: 0 0 8px rgba(0, 255, 0, 0.5);
}

/* 无人机标记 */
.drone-marker {
  position: absolute;
  z-index: 100;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

/* 雷达环 */
.drone-radar-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80px;
  height: 80px;
  border: 2px solid rgba(255, 69, 0, 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: radar-ring 3s infinite;
}

.drone-radar-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  border: 2px solid rgba(255, 69, 0, 0.6);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: radar-pulse 2s infinite;
}

@keyframes radar-ring {
  0% {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes radar-pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.4;
  }
  100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.8;
  }
}

.drone-icon-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 无人机机身 */
.drone-body {
  position: relative;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 无人机中心 */
.drone-center {
  position: absolute;
  width: 16px;
  height: 16px;
  background: linear-gradient(45deg, #ff4500, #ff6b35);
  border: 2px solid #fff;
  border-radius: 50%;
  box-shadow: 0 0 15px rgba(255, 69, 0, 0.8);
  z-index: 10;
}

.drone-led {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 6px;
  background: #fff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: led-blink 1s infinite;
}

@keyframes led-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

/* 无人机臂 */
.drone-arm {
  position: absolute;
  width: 20px;
  height: 3px;
  background: linear-gradient(90deg, #333, #666);
  border-radius: 2px;
  transform-origin: center;
}

.drone-arm-1 {
  top: 8px;
  left: 8px;
  transform: rotate(45deg);
}

.drone-arm-2 {
  top: 8px;
  right: 8px;
  transform: rotate(-45deg);
}

.drone-arm-3 {
  bottom: 8px;
  left: 8px;
  transform: rotate(-45deg);
}

.drone-arm-4 {
  bottom: 8px;
  right: 8px;
  transform: rotate(45deg);
}

/* 螺旋桨 */
.drone-propeller {
  position: absolute;
  width: 12px;
  height: 12px;
  border: 2px solid #ff4500;
  border-radius: 50%;
  background: rgba(255, 69, 0, 0.1);
  animation: propeller-spin 0.1s linear infinite;
}

.drone-arm-1 .drone-propeller {
  top: -6px;
  right: -6px;
}

.drone-arm-2 .drone-propeller {
  top: -6px;
  left: -6px;
}

.drone-arm-3 .drone-propeller {
  bottom: -6px;
  right: -6px;
}

.drone-arm-4 .drone-propeller {
  bottom: -6px;
  left: -6px;
}

@keyframes propeller-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 无人机标签 */
.drone-label {
  margin-top: 8px;
  padding: 2px 8px;
  background: rgba(255, 69, 0, 0.9);
  color: white;
  font-size: 10px;
  font-weight: bold;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.drone-status {
  margin-top: 2px;
  padding: 1px 6px;
  background: rgba(0, 255, 0, 0.8);
  color: white;
  font-size: 8px;
  font-weight: bold;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}




/* 无人机控制按钮 - 半透明风格 */
.drone-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 1000;
  padding: 10px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.drone-control-btn {
  width: 56px;
  height: 56px;
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.drone-control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.drone-control-btn.active {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow:
    0 0 20px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.drone-control-btn.emergency {
  border-color: rgba(255, 107, 71, 0.4);
}

.drone-control-btn.emergency:hover {
  background: rgba(255, 107, 71, 0.1);
  border-color: rgba(255, 107, 71, 0.6);
  box-shadow:
    0 6px 20px rgba(255, 107, 71, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.drone-control-btn.emergency.active {
  background: rgba(255, 107, 71, 0.2);
  border-color: rgba(255, 107, 71, 0.8);
  box-shadow:
    0 0 20px rgba(255, 107, 71, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.drone-control-btn i {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
}

.drone-control-btn.emergency i {
  color: rgba(255, 107, 71, 1);
}

.btn-label {
  font-size: 9px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  text-align: center;
}

/* 响应式设计 - 控制按钮 */
@media (max-width: 768px) {
  .drone-controls {
    bottom: 15px;
    gap: 6px;
    padding: 8px;
  }
  
  .drone-control-btn {
    width: 48px;
    height: 48px;
  }
  
  .drone-control-btn i {
    font-size: 16px;
    margin-bottom: 1px;
  }
  
  .btn-label {
    font-size: 8px;
  }
}

@media (max-width: 480px) {
  .drone-controls {
    bottom: 10px;
    gap: 4px;
    padding: 6px;
  }
  
  .drone-control-btn {
    width: 42px;
    height: 42px;
  }
  
  .drone-control-btn i {
    font-size: 14px;
    margin-bottom: 1px;
  }
  
  .btn-label {
    font-size: 7px;
  }
}

/* 地图控制按钮 - 半透明风格 */
.map-controls {
  position: absolute;
  top: 90px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 1000;
}

.map-control-btn {
  width: 44px;
  height: 44px;
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
  box-shadow:
    0 2px 10px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.map-control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.map-control-btn:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.05);
}

.map-control-btn i {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
}



/* 状态提示 */
.status-toast {
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(15, 25, 40, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 1001;
  animation: toast-slide-in 0.3s ease;
}

@keyframes toast-slide-in {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-15px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .flight-hud {
    min-width: 600px;
    max-width: 90%;
  }
}

@media (max-width: 768px) {
  .flight-hud {
    min-width: 400px;
    max-width: 95%;
    height: 45px;
    padding: 0 15px;
  }

  .hud-left, .hud-center, .hud-right {
    font-size: 11px;
  }

  .map-controls {
    top: 80px;
  }
}

@media (max-width: 480px) {
  .flight-hud {
    min-width: 320px;
    max-width: 98%;
    height: 40px;
    padding: 0 10px;
  }

  .hud-left, .hud-center, .hud-right {
    font-size: 10px;
  }
}


</style>