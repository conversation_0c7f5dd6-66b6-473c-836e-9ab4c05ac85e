// 演示数据初始化工具
import { waypointStore } from '@/stores/WaypointStore.js'
import { Waypoint, Mission } from '@/models/WaypointModels.js'

// 创建演示航点数据
export function createDemoWaypoints() {
  const demoWaypoints = [
    // 起飞点
    new Waypoint({
      name: '起飞点',
      type: 'TAKEOFF',
      latitude: 39.9042,
      longitude: 116.4074,
      altitude: 10,
      relativeAltitude: 10,
      speed: 3.0,
      sequence: 0,
      status: 'COMPLETED'
    }),
    
    // 巡航航点
    new Waypoint({
      name: '巡航点1',
      type: 'WAYPOINT',
      latitude: 39.9052,
      longitude: 116.4084,
      altitude: 50,
      relativeAltitude: 50,
      speed: 8.0,
      sequence: 1,
      status: 'COMPLETED'
    }),
    
    new Waypoint({
      name: '拍照点1',
      type: 'PHOTO',
      latitude: 39.9062,
      longitude: 116.4094,
      altitude: 45,
      relativeAltitude: 45,
      speed: 5.0,
      sequence: 2,
      status: 'ACTIVE',
      cameraAction: 'PHOTO',
      gimbalPitch: -30,
      holdTime: 3
    }),
    
    new Waypoint({
      name: '巡航点2',
      type: 'WAYPOINT',
      latitude: 39.9072,
      longitude: 116.4104,
      altitude: 60,
      relativeAltitude: 60,
      speed: 10.0,
      sequence: 3,
      status: 'PENDING'
    }),
    
    new Waypoint({
      name: '录像开始点',
      type: 'VIDEO_START',
      latitude: 39.9082,
      longitude: 116.4114,
      altitude: 55,
      relativeAltitude: 55,
      speed: 6.0,
      sequence: 4,
      status: 'PENDING',
      cameraAction: 'VIDEO_START',
      gimbalPitch: -45
    }),
    
    new Waypoint({
      name: '悬停点',
      type: 'LOITER',
      latitude: 39.9092,
      longitude: 116.4124,
      altitude: 50,
      relativeAltitude: 50,
      speed: 2.0,
      sequence: 5,
      status: 'PENDING',
      holdTime: 10
    }),
    
    new Waypoint({
      name: '录像结束点',
      type: 'VIDEO_STOP',
      latitude: 39.9102,
      longitude: 116.4134,
      altitude: 55,
      relativeAltitude: 55,
      speed: 6.0,
      sequence: 6,
      status: 'PENDING',
      cameraAction: 'VIDEO_STOP'
    }),
    
    new Waypoint({
      name: '返航点',
      type: 'RTL',
      latitude: 39.9042,
      longitude: 116.4074,
      altitude: 30,
      relativeAltitude: 30,
      speed: 8.0,
      sequence: 7,
      status: 'PENDING'
    }),
    
    // 降落点
    new Waypoint({
      name: '降落点',
      type: 'LANDING',
      latitude: 39.9042,
      longitude: 116.4074,
      altitude: 0,
      relativeAltitude: 0,
      speed: 2.0,
      sequence: 8,
      status: 'PENDING'
    })
  ]
  
  return demoWaypoints
}

// 创建演示任务
export function createDemoMission() {
  const demoWaypoints = createDemoWaypoints()
  
  const mission = new Mission({
    name: '演示巡航任务',
    description: '这是一个包含拍照、录像和悬停的演示任务',
    waypoints: demoWaypoints,
    homePosition: {
      latitude: 39.9042,
      longitude: 116.4074,
      altitude: 0
    },
    takeoffAltitude: 10,
    defaultSpeed: 6.0,
    rtlAltitude: 30,
    status: 'RUNNING'
  })
  
  return mission
}

// 创建大量测试航点（用于性能测试）
export function createLargeDataset(count = 1000) {
  const waypoints = []
  const baseLatitude = 39.9042
  const baseLongitude = 116.4074
  
  // 生成网格状分布的航点
  const gridSize = Math.ceil(Math.sqrt(count))
  const latStep = 0.01 / gridSize
  const lngStep = 0.01 / gridSize
  
  for (let i = 0; i < count; i++) {
    const row = Math.floor(i / gridSize)
    const col = i % gridSize
    
    const waypoint = new Waypoint({
      name: `测试点_${i + 1}`,
      type: getRandomWaypointType(),
      latitude: baseLatitude + (row * latStep),
      longitude: baseLongitude + (col * lngStep),
      altitude: 30 + Math.random() * 70, // 30-100米随机高度
      relativeAltitude: 30 + Math.random() * 70,
      speed: 3 + Math.random() * 12, // 3-15m/s随机速度
      sequence: i,
      status: getRandomStatus(),
      holdTime: Math.random() > 0.8 ? Math.floor(Math.random() * 10) : 0
    })
    
    waypoints.push(waypoint)
  }
  
  return waypoints
}

// 获取随机航点类型
function getRandomWaypointType() {
  const types = ['WAYPOINT', 'PHOTO', 'VIDEO_START', 'VIDEO_STOP', 'LOITER']
  const weights = [0.6, 0.15, 0.1, 0.1, 0.05] // 权重分布
  
  const random = Math.random()
  let cumulative = 0
  
  for (let i = 0; i < types.length; i++) {
    cumulative += weights[i]
    if (random <= cumulative) {
      return types[i]
    }
  }
  
  return 'WAYPOINT'
}

// 获取随机状态
function getRandomStatus() {
  const statuses = ['PENDING', 'ACTIVE', 'COMPLETED', 'FAILED', 'SKIPPED']
  const weights = [0.5, 0.1, 0.3, 0.05, 0.05]
  
  const random = Math.random()
  let cumulative = 0
  
  for (let i = 0; i < statuses.length; i++) {
    cumulative += weights[i]
    if (random <= cumulative) {
      return statuses[i]
    }
  }
  
  return 'PENDING'
}

// 初始化演示数据
export function initializeDemoData() {
  try {
    // 检查是否已有数据
    if (waypointStore.state.totalWaypoints > 0) {
      console.log('航点数据已存在，跳过初始化')
      return
    }
    
    console.log('正在初始化演示数据...')
    
    // 创建演示航点
    const demoWaypoints = createDemoWaypoints()
    demoWaypoints.forEach(waypoint => {
      waypointStore.createWaypoint(waypoint)
    })
    
    // 创建演示任务
    const demoMission = createDemoMission()
    waypointStore.createMission(demoMission)
    waypointStore.setCurrentMission(demoMission.id)
    
    console.log(`✅ 演示数据初始化完成: ${demoWaypoints.length} 个航点, 1 个任务`)
    
    return {
      waypoints: demoWaypoints,
      mission: demoMission
    }
  } catch (error) {
    console.error('演示数据初始化失败:', error)
    throw error
  }
}

// 清除所有数据
export function clearAllData() {
  try {
    // 清除所有航点
    const waypointIds = Array.from(waypointStore.state.waypoints.keys())
    waypointStore.deleteWaypointsBatch(waypointIds)
    
    // 清除所有任务
    waypointStore.state.missions.clear()
    waypointStore.state.currentMission = null
    waypointStore.state.totalMissions = 0
    waypointStore.state.activeMissions = 0
    
    // 重置状态
    waypointStore.state.selectedWaypoints.clear()
    waypointStore.state.bulkEditSelection.clear()
    waypointStore.state.editingWaypoint = null
    waypointStore.state.isCreatingWaypoint = false
    
    console.log('✅ 所有数据已清除')
  } catch (error) {
    console.error('清除数据失败:', error)
    throw error
  }
}

// 性能测试数据初始化
export function initializePerformanceTestData(waypointCount = 5000) {
  try {
    console.log(`正在创建 ${waypointCount} 个测试航点...`)
    
    const startTime = performance.now()
    
    // 清除现有数据
    clearAllData()
    
    // 创建大量测试数据
    const testWaypoints = createLargeDataset(waypointCount)
    
    // 批量创建航点
    const batchSize = 100
    let processed = 0
    
    const processBatch = () => {
      const batch = testWaypoints.slice(processed, processed + batchSize)
      batch.forEach(waypoint => {
        waypointStore.createWaypoint(waypoint)
      })
      
      processed += batch.length
      
      if (processed < testWaypoints.length) {
        // 使用 setTimeout 避免阻塞 UI
        setTimeout(processBatch, 0)
      } else {
        const endTime = performance.now()
        const duration = endTime - startTime
        
        console.log(`✅ 性能测试数据创建完成:`)
        console.log(`   - 航点数量: ${waypointCount}`)
        console.log(`   - 创建时间: ${duration.toFixed(2)}ms`)
        console.log(`   - 平均速度: ${(waypointCount / duration * 1000).toFixed(0)} 航点/秒`)
      }
    }
    
    processBatch()
    
  } catch (error) {
    console.error('性能测试数据初始化失败:', error)
    throw error
  }
}

// 导出工具函数
export const DemoDataUtils = {
  createDemoWaypoints,
  createDemoMission,
  createLargeDataset,
  initializeDemoData,
  clearAllData,
  initializePerformanceTestData
}
