<template>
  <div class="cyber-compass" :style="{ width: size + 'px', height: size + 'px' }">
    <!-- 外层发光环 -->
    <div class="compass-ring">
      <div class="ring-glow" :style="{ transform: `rotate(${glowAngle}deg)` }"></div>
    </div>
    
    <!-- 罗盘主体 -->
    <div class="compass-body">
      <!-- 网格背景 -->
      <div class="grid-pattern"></div>
      
      <!-- 旋转的罗盘面 -->
      <div class="compass-face" :style="{ transform: `rotate(${-heading}deg)` }">
        <!-- SVG刻度和方向 -->
        <svg viewBox="0 0 200 200" class="compass-svg">
          <!-- 主要方向标记 -->
          <g v-for="direction in majorDirections" :key="direction.value">
            <!-- 刻度线 -->
            <line 
              :x1="100 + Math.sin(direction.value * Math.PI / 180) * 85"
              :y1="100 - Math.cos(direction.value * Math.PI / 180) * 85"
              :x2="100 + Math.sin(direction.value * Math.PI / 180) * 70"
              :y2="100 - Math.cos(direction.value * Math.PI / 180) * 70"
              stroke="#00ffff" 
              stroke-width="3"
              class="major-mark" />
            
            <!-- 方向字母 -->
            <text 
              :x="100 + Math.sin(direction.value * Math.PI / 180) * 60"
              :y="100 - Math.cos(direction.value * Math.PI / 180) * 60 + 4"
              text-anchor="middle" 
              class="direction-label"
              :class="{ 'north': direction.value === 0 }"
              fill="#ffffff"
              font-size="16"
              font-weight="bold">
              {{ direction.label }}
            </text>
          </g>
          
          <!-- 次要刻度 -->
          <g v-for="angle in minorAngles" :key="angle">
            <line 
              :x1="100 + Math.sin(angle * Math.PI / 180) * 85"
              :y1="100 - Math.cos(angle * Math.PI / 180) * 85"
              :x2="100 + Math.sin(angle * Math.PI / 180) * 80"
              :y2="100 - Math.cos(angle * Math.PI / 180) * 80"
              stroke="rgba(0, 255, 255, 0.6)" 
              stroke-width="1"
              class="minor-mark" />
          </g>
        </svg>
      </div>
      
      <!-- 固定的指针 -->
      <div class="compass-pointer">
        <div class="pointer-arrow"></div>
      </div>
      
      <!-- 中心数字显示 -->
      <div class="center-display">
        <div class="heading-value">{{ Math.round(heading) }}</div>
        <div class="heading-unit">°</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CyberCompass',
  props: {
    heading: {
      type: Number,
      default: 0
    },
    size: {
      type: Number,
      default: 200
    }
  },
  data() {
    return {
      glowAngle: 0,
      majorDirections: [
        { value: 0, label: 'N' },
        { value: 90, label: 'E' },
        { value: 180, label: 'S' },
        { value: 270, label: 'W' }
      ],
      minorAngles: [],
      outerTicks: []
    }
  },
  mounted() {
    this.initializeAngles()
    // 暂时不启动发光动画，保持静态
    // this.startGlowAnimation()
  },
  beforeUnmount() {
    if (this.glowInterval) {
      clearInterval(this.glowInterval)
    }
  },
  methods: {
    initializeAngles() {
      // 次要角度刻度
      this.minorAngles = []
      for (let i = 0; i < 360; i += 10) {
        this.minorAngles.push(i)
      }
      
      // 外圈刻度
      this.outerTicks = []
      for (let i = 0; i < 360; i += 5) {
        this.outerTicks.push(i)
      }
    },
    
    startGlowAnimation() {
      this.glowInterval = setInterval(() => {
        this.glowAngle += 1
        if (this.glowAngle >= 360) {
          this.glowAngle = 0
        }
      }, 100)
    }
  }
}
</script>

<style scoped>
  .cyber-compass {
    position: relative;
    border-radius: 10px; /* 统一圆角 */
    overflow: hidden;
    background: rgba(0, 0, 0, 0.2); /* 统一背景 */
    border: 1px solid rgba(255, 255, 255, 0.1); /* 统一边框 */
  }
  
  .compass-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 10px; /* 匹配外框 */
    z-index: 1;
  }
  
  .ring-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    background: linear-gradient(
      45deg,
      transparent 40%,
      rgba(0, 255, 255, 0.15) 50%,
      transparent 60%
    );
    /* 静态显示，无动画 */
  }
  
  .compass-body {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 10;
  }
  
  .grid-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    background: 
      radial-gradient(circle at 50% 50%, 
        rgba(0, 255, 255, 0.05) 1px, 
        transparent 1px);
    background-size: 15px 15px; /* 更精细的网格 */
    opacity: 0.4;
  }
  
  .compass-face {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1); /* 平滑过渡 */
  }
  
  .compass-svg {
    width: 100%;
    height: 100%;
  }
  
  .major-mark {
    stroke: rgba(0, 255, 255, 0.8);
    stroke-width: 2.5; /* 稍粗一些 */
    filter: drop-shadow(0 0 3px rgba(0, 255, 255, 0.4));
  }
  
  .direction-label {
    font-size: 16px; /* 增大字体 */
    font-weight: 700;
    fill: rgba(255, 255, 255, 0.9);
    text-shadow: 0 0 5px rgba(0, 0, 0, 1);
  }
  
  .direction-label.north {
    fill: #ff4757 !important; /* 醒目的红色 */
    font-size: 18px !important;
    text-shadow: 0 0 8px rgba(255, 71, 87, 0.8) !important;
  }
  
  .minor-mark {
    stroke: rgba(0, 255, 255, 0.5);
    stroke-width: 1.5;
  }

  .compass-pointer {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 50%;
    z-index: 15;
  }

  .pointer-arrow {
    position: absolute;
    top: 15px; /* 调整位置 */
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 12px solid #ff4757; /* 统一颜色 */
    filter: drop-shadow(0 0 8px rgba(255, 71, 87, 0.7));
  }

  .pointer-arrow::after {
    content: '';
    position: absolute;
    top: -17px;
    left: -1px;
    width: 2px;
    height: 15px;
    background: #ff4757;
  }

  /* 中心显示 */
  .center-display {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 20;
    background: rgba(0, 0, 0, 0.8);
    padding: 8px 12px; /* 调整内边距 */
    border-radius: 8px;
    border: 1px solid rgba(0, 255, 255, 0.3);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
    min-width: auto; /* 自动宽度 */
    display: flex; /* 使用Flex布局 */
    align-items: baseline; /* 基线对齐 */
    gap: 3px; /* 添加间隙 */
  }
  
  .heading-value {
    font-size: 22px; /* 减小字体大小 */
    font-weight: 700;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
    line-height: 1;
  }
  
  .heading-unit {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    padding-bottom: 1px; /* 微调对齐 */
  }

  /* 移除不再使用的样式 */
  .outer-ring, .outer-tick {
    display: none;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .direction-label {
      font-size: 14px;
    }
    
    .direction-label.north {
      font-size: 16px;
    }
    
    .angle-label {
      font-size: 8px;
    }
    
    .heading-value {
      font-size: 24px;
    }
    
    .heading-label {
      font-size: 10px;
    }
  }
</style> 