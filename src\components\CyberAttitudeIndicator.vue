<template>
  <div class="modern-attitude-indicator" :style="{ width: size + 'px', height: size + 'px' }">
    <!-- Background with glow effect -->
    <div class="indicator-background"></div>

    <!-- Main SVG Container -->
    <svg :viewBox="viewBox" class="indicator-svg">
      <!-- Defs for gradients, filters and patterns -->
      <defs>
        <!-- Main clipping path -->
        <clipPath id="main-clip">
          <circle cx="0" cy="0" :r="radius - 5" />
        </clipPath>

        <!-- Glow filter -->
        <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>

        <!-- Modern sky gradient -->
        <radialGradient id="sky-modern" cx="50%" cy="30%">
          <stop offset="0%" style="stop-color: #00d4ff;" />
          <stop offset="50%" style="stop-color: #0099cc;" />
          <stop offset="100%" style="stop-color: #003d5c;" />
        </radialGradient>

        <!-- Modern ground gradient -->
        <radialGradient id="ground-modern" cx="50%" cy="70%">
          <stop offset="0%" style="stop-color: #ff6b35;" />
          <stop offset="50%" style="stop-color: #cc4400;" />
          <stop offset="100%" style="stop-color: #662200;" />
        </radialGradient>

        <!-- Grid pattern -->
        <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
          <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(0,255,255,0.1)" stroke-width="0.5"/>
        </pattern>

        <!-- Cyber ring gradient -->
        <linearGradient id="cyber-ring-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color: #00ffff;" />
          <stop offset="50%" style="stop-color: #0099cc;" />
          <stop offset="100%" style="stop-color: #00ffff;" />
        </linearGradient>
      </defs>

      <!-- Outer ring with cyber styling -->
      <circle cx="0" cy="0" :r="radius - 2"
              fill="none"
              stroke="url(#cyber-ring-gradient)"
              stroke-width="2"
              class="outer-ring" />

      <!-- Inner rotating horizon -->
      <g :style="horizonTransform" clip-path="url(#main-clip)">
        <!-- Sky section -->
        <rect :x="-size * 1.5" :y="-size * 3" :width="size * 3" :height="size * 3"
              fill="url(#sky-modern)" />

        <!-- Ground section -->
        <rect :x="-size * 1.5" :y="0" :width="size * 3" :height="size * 3"
              fill="url(#ground-modern)" />

        <!-- Horizon line with glow -->
        <line :x1="-size * 1.5" y1="0" :x2="size * 1.5" y2="0"
              stroke="#00ffff"
              stroke-width="2"
              filter="url(#glow)"
              class="horizon-line" />

        <!-- Modern pitch ladder -->
        <g class="pitch-ladder">
          <g v-for="angle in pitchAngles" :key="angle" class="pitch-line-group">
            <!-- Major pitch lines (每10度) -->
            <g v-if="Math.abs(angle) % 10 === 0 && angle !== 0">
              <line :x1="-50" :y1="-angle * pitchScale" :x2="50" :y2="-angle * pitchScale"
                    stroke="#00ffff" stroke-width="2" opacity="0.9" />
              <text :x="-55" :y="-angle * pitchScale + 4"
                    class="pitch-text major"
                    text-anchor="end">{{ Math.abs(angle) }}</text>
              <text :x="55" :y="-angle * pitchScale + 4"
                    class="pitch-text major"
                    text-anchor="start">{{ Math.abs(angle) }}</text>
            </g>

            <!-- Minor pitch lines (每5度) -->
            <g v-else-if="Math.abs(angle) % 5 === 0 && angle !== 0">
              <line :x1="-25" :y1="-angle * pitchScale" :x2="25" :y2="-angle * pitchScale"
                    stroke="#00ffff" stroke-width="1" opacity="0.6" />
            </g>
          </g>
        </g>

        <!-- Grid overlay for tech feel -->
        <rect :x="-size * 1.5" :y="-size * 1.5" :width="size * 3" :height="size * 3"
              fill="url(#grid)" opacity="0.3" />
      </g>

      <!-- Center aircraft symbol - modern design -->
      <g class="aircraft-symbol">
        <!-- Main body -->
        <circle cx="0" cy="0" r="3" fill="#00ffff" filter="url(#glow)" />

        <!-- Wings -->
        <path d="M -35 0 L -8 0 M 35 0 L 8 0"
              stroke="#00ffff"
              stroke-width="3"
              stroke-linecap="round"
              filter="url(#glow)" />

        <!-- Nose indicator -->
        <path d="M 0 -8 L -4 4 L 4 4 Z"
              fill="none"
              stroke="#00ffff"
              stroke-width="2"
              stroke-linejoin="round"
              filter="url(#glow)" />
      </g>

      <!-- Roll scale with modern styling -->
      <g class="roll-scale">
        <!-- Roll indicator triangle -->
        <path d="M 0 -92 L -6 -82 L 6 -82 Z"
              fill="#ff6b35"
              filter="url(#glow)"
              class="roll-indicator" />

        <!-- Roll scale marks -->
        <g v-for="angle in rollMarks" :key="angle" :transform="`rotate(${angle})`">
          <line x1="0" :y1="-radius + 2" x2="0" :y2="-radius + (isMajorRollMark(angle) ? 12 : 8)"
                stroke="#00ffff"
                :stroke-width="isMajorRollMark(angle) ? 2 : 1"
                opacity="0.8" />

          <!-- Roll angle labels -->
          <text v-if="isMajorRollMark(angle) && angle !== 0"
                :transform="`rotate(${-angle})`"
                x="0" :y="-radius + 22"
                class="roll-text"
                text-anchor="middle">{{ Math.abs(angle) }}°</text>
        </g>
      </g>

      <!-- Digital readouts -->
      <g class="digital-readouts">
        <!-- Pitch readout -->
        <rect x="-25" y="50" width="50" height="14"
              fill="rgba(0,0,0,0.8)"
              stroke="#00ffff"
              stroke-width="1"
              rx="2" />
        <text x="0" y="60" class="digital-text" text-anchor="middle">
          P: {{ pitch.toFixed(1) }}°
        </text>

        <!-- Roll readout -->
        <rect x="-25" y="66" width="50" height="14"
              fill="rgba(0,0,0,0.8)"
              stroke="#00ffff"
              stroke-width="1"
              rx="2" />
        <text x="0" y="76" class="digital-text" text-anchor="middle">
          R: {{ roll.toFixed(1) }}°
        </text>
      </g>
    </svg>

    <!-- Corner indicators -->
    <div class="corner-indicators">
      <div class="corner-indicator top-left"></div>
      <div class="corner-indicator top-right"></div>
      <div class="corner-indicator bottom-left"></div>
      <div class="corner-indicator bottom-right"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CyberAttitudeIndicator',
  props: {
    pitch: { type: Number, default: -15 }, // 静态数据：向下俯仰15度
    roll: { type: Number, default: 25 },   // 静态数据：向右滚转25度
    size: { type: Number, default: 200 },
  },
  computed: {
    viewBox() {
      const s = this.size / 2;
      return `${-s} ${-s} ${this.size} ${this.size}`;
    },
    radius() {
      return this.size / 2 - 5;
    },
    pitchScale() {
      return 1.5; // 减小俯仰刻度，像素每度
    },
    horizonTransform() {
      const pitchOffset = this.pitch * this.pitchScale;
      return {
        transform: `rotate(${-this.roll}deg) translateY(${pitchOffset}px)`,
        // 移除动画过渡，避免晃动
      };
    },
    pitchAngles() {
      // 生成俯仰角度，范围更大，间隔更密
      const angles = [];
      for (let i = -90; i <= 90; i += 5) {
        angles.push(i);
      }
      return angles;
    },
    rollMarks() {
      // 扩展滚转刻度范围，包含更多角度
      return [-90, -60, -45, -30, -20, -10, 0, 10, 20, 30, 45, 60, 90];
    },
  },
  methods: {
    isMajorRollMark(angle) {
      return angle === 0 || Math.abs(angle) === 30 || Math.abs(angle) === 60 || Math.abs(angle) === 90;
    },
  },
};
</script>

<style scoped>
.modern-attitude-indicator {
  position: relative;
  font-family: 'Orbitron', 'Courier New', monospace;
  font-weight: 400;
  border-radius: 50%;
  overflow: hidden;
  box-shadow:
    0 0 20px rgba(0, 255, 255, 0.3),
    inset 0 0 20px rgba(0, 0, 0, 0.5);
}

.indicator-background {
  position: absolute;
  top: 2px;
  left: 2px;
  width: calc(100% - 4px);
  height: calc(100% - 4px);
  background: radial-gradient(circle at center,
    rgba(0, 20, 40, 0.7) 0%,
    rgba(0, 10, 20, 0.8) 70%,
    rgba(0, 0, 0, 0.9) 100%);
  border-radius: 50%;
}

.indicator-svg {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
}

/* Outer ring animation */
.outer-ring {
  animation: pulse-ring 2s ease-in-out infinite alternate;
}

@keyframes pulse-ring {
  0% { stroke-opacity: 0.6; }
  100% { stroke-opacity: 1; }
}

/* Horizon line glow effect */
.horizon-line {
  filter: drop-shadow(0 0 5px #00ffff);
}

/* Pitch ladder styling */
.pitch-text {
  fill: #00ffff;
  font-size: 8px;
  font-family: 'Orbitron', monospace;
  font-weight: 500;
}

.pitch-text.major {
  font-size: 9px;
  font-weight: 600;
}

/* Aircraft symbol glow */
.aircraft-symbol {
  filter: drop-shadow(0 0 3px #00ffff);
}

/* Roll scale styling */
.roll-indicator {
  animation: pulse-indicator 1.5s ease-in-out infinite alternate;
}

@keyframes pulse-indicator {
  0% { fill-opacity: 0.8; }
  100% { fill-opacity: 1; }
}

.roll-text {
  fill: #00ffff;
  font-size: 8px;
  font-family: 'Orbitron', monospace;
  font-weight: 500;
}

/* Digital readouts */
.digital-text {
  fill: #00ffff;
  font-size: 10px;
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  text-shadow: 0 0 5px #00ffff;
}

/* Corner indicators */
.corner-indicators {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
}

.corner-indicator {
  position: absolute;
  width: 15px;
  height: 15px;
  border: 2px solid #00ffff;
  opacity: 0.6;
}

.corner-indicator.top-left {
  top: 10px;
  left: 10px;
  border-right: none;
  border-bottom: none;
}

.corner-indicator.top-right {
  top: 10px;
  right: 10px;
  border-left: none;
  border-bottom: none;
}

.corner-indicator.bottom-left {
  bottom: 10px;
  left: 10px;
  border-right: none;
  border-top: none;
}

.corner-indicator.bottom-right {
  bottom: 10px;
  right: 10px;
  border-left: none;
  border-top: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pitch-text, .roll-text {
    font-size: 7px;
  }

  .digital-text {
    font-size: 9px;
  }
}
</style>