<template>
  <div class="attitude-indicator" :style="{ width: size + 'px', height: size + 'px' }">
    <svg :viewBox="viewBox" class="indicator-svg">
      <!-- Defs for gradients and clipping -->
      <defs>
        <clipPath id="attitude-clip-path">
          <circle cx="0" cy="0" :r="radius" />
        </clipPath>
        <linearGradient id="sky-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color: #3498db;" />
          <stop offset="100%" style="stop-color: #85c1e9;" />
        </linearGradient>
        <linearGradient id="ground-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color: #8B4513;" />
          <stop offset="100%" style="stop-color: #D2B48C;" />
        </linearGradient>
      </defs>

      <!-- Bezel and Background -->
      <circle cx="0" cy="0" :r="radius" fill="#1c2833" stroke="rgba(255,255,255,0.1)" stroke-width="1" />
      
      <!-- Rotating Group for Pitch/Roll -->
      <g :style="dynamicTransform" clip-path="url(#attitude-clip-path)">
        <!-- Sky & Ground -->
        <rect :x="-size" :y="-size * 1.5" :width="size * 2" :height="size * 1.5" fill="url(#sky-gradient)" />
        <rect :x="-size" :y="0" :width="size * 2" :height="size * 1.5" fill="url(#ground-gradient)" />
        <line :x1="-size" y1="0" :x2="size" y2="0" stroke="#ecf0f1" stroke-width="1.5" />

        <!-- Pitch Ladder -->
        <g class="pitch-ladder" stroke="#fff" stroke-width="1">
          <g v-for="p in [10, 20, 30, 40, 50, 60]" :key="p">
            <line :x1="-30" :y1="-p * pitchScale" :x2="30" :y2="-p * pitchScale" />
            <line :x1="-30" :y1="p * pitchScale" :x2="30" :y2="p * pitchScale" />
            <text :x="-35" :y="-p * pitchScale + 4" class="pitch-label" text-anchor="end">{{ p }}</text>
            <text :x="35" :y="-p * pitchScale + 4" class="pitch-label" text-anchor="start">{{ p }}</text>
          </g>
        </g>
      </g>

      <!-- Fixed Aircraft Symbol in the Center -->
      <g class="aircraft-symbol" fill="#f1c40f" stroke="#f1c40f" stroke-width="2" stroke-linejoin="round">
        <path d="M -40 0 L -10 0" />
        <path d="M 40 0 L 10 0" />
        <path d="M -7 7 L 0 0 L 7 7" fill="none" />
      </g>

      <!-- Fixed Roll Scale on top -->
      <g class="roll-scale" fill="#fff" font-size="10" text-anchor="middle">
        <path d="M 0 -83 L -5 -78 L 5 -78 Z" fill="#e74c3c" />
        <g v-for="angle in [-60, -45, -30, -20, -10, 10, 20, 30, 45, 60]" :key="angle" :transform="`rotate(${angle})`">
          <line x1="0" :y1="-radius" x2="0" :y2="-radius + (angle % 30 === 0 ? 10 : 5)" stroke="#fff" stroke-width="1.5" />
        </g>
        <g v-for="angle in [-60, -30, 30, 60]" :key="angle+'t'" :transform="`rotate(${angle})`">
          <text :transform="`rotate(${-angle})`" x="0" y="-radius + 20">{{ Math.abs(angle) }}</text>
        </g>
      </g>
    </svg>
  </div>
</template>

<script>
export default {
  name: 'CyberAttitudeIndicator',
  props: {
    pitch: { type: Number, default: 0 },
    roll: { type: Number, default: 0 },
    size: { type: Number, default: 180 },
  },
  computed: {
    viewBox() {
      const s = this.size / 2;
      return `${-s} ${-s} ${this.size} ${this.size}`;
    },
    radius() {
      return this.size / 2 - 5;
    },
    pitchScale() {
      return 3;
    },
    dynamicTransform() {
      const pitchOffset = this.pitch * this.pitchScale;
      // The SVG rotates opposite to the roll
      return {
        transform: `rotate(${-this.roll}deg) translateY(${pitchOffset}px)`,
        transition: 'transform 0.1s linear',
      };
    },
  },
};
</script>

<style scoped>
.attitude-indicator {
  position: relative;
  font-family: 'Segoe UI', sans-serif;
  font-weight: 600;
}
.indicator-svg {
  /* The indicator itself doesn't rotate, the inner parts do */
}
.pitch-label {
  fill: #fff;
  font-size: 10px;
}
.aircraft-symbol, .roll-scale {
  /* These are static, so no transition needed here */
}
</style> 