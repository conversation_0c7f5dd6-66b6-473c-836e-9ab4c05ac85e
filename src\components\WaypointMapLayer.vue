<template>
  <div class="waypoint-map-layer">
    <!-- 航点创建模式提示 -->
    <div v-if="isCreatingWaypoint" class="creation-hint">
      <i class="fas fa-mouse-pointer"></i>
      点击地图创建{{ waypointTypeInfo.name }}
      <button @click="cancelCreation" class="cancel-btn">
        <i class="fas fa-times"></i>
      </button>
    </div>
    
    <!-- 航点信息弹窗 -->
    <div v-if="selectedWaypoint" class="waypoint-popup" :style="popupStyle">
      <div class="popup-header">
        <h4>{{ selectedWaypoint.name }}</h4>
        <button @click="closePopup" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="popup-content">
        <div class="info-row">
          <span class="label">类型:</span>
          <span class="value">{{ getWaypointTypeInfo(selectedWaypoint.type).name }}</span>
        </div>
        <div class="info-row">
          <span class="label">坐标:</span>
          <span class="value">{{ formatCoordinates(selectedWaypoint) }}</span>
        </div>
        <div class="info-row">
          <span class="label">高度:</span>
          <span class="value">{{ selectedWaypoint.altitude }}m</span>
        </div>
        <div class="info-row">
          <span class="label">速度:</span>
          <span class="value">{{ selectedWaypoint.speed }}m/s</span>
        </div>
        <div class="info-row">
          <span class="label">状态:</span>
          <span class="value" :class="'status-' + selectedWaypoint.status.toLowerCase()">
            {{ selectedWaypoint.status }}
          </span>
        </div>
      </div>
      <div class="popup-actions">
        <button @click="editWaypoint" class="edit-btn">
          <i class="fas fa-edit"></i> 编辑
        </button>
        <button @click="deleteWaypoint" class="delete-btn">
          <i class="fas fa-trash"></i> 删除
        </button>
      </div>
    </div>
    
    <!-- 批量操作工具栏 -->
    <div v-if="bulkEditMode" class="bulk-edit-toolbar">
      <div class="selection-info">
        已选择 {{ bulkEditSelection.size }} 个航点
      </div>
      <div class="bulk-actions">
        <button @click="bulkDelete" class="bulk-btn delete">
          <i class="fas fa-trash"></i> 删除
        </button>
        <button @click="bulkEdit" class="bulk-btn edit">
          <i class="fas fa-edit"></i> 编辑
        </button>
        <button @click="createMissionFromSelection" class="bulk-btn mission">
          <i class="fas fa-route"></i> 创建任务
        </button>
        <button @click="exitBulkMode" class="bulk-btn cancel">
          <i class="fas fa-times"></i> 取消
        </button>
      </div>
    </div>
    
    <!-- 性能统计 -->
    <div v-if="showPerformanceStats" class="performance-stats">
      <div class="stat-item">
        <span class="label">可见:</span>
        <span class="value">{{ visibleWaypoints.size }}</span>
      </div>
      <div class="stat-item">
        <span class="label">聚合:</span>
        <span class="value">{{ waypointClusters.size }}</span>
      </div>
      <div class="stat-item">
        <span class="label">FPS:</span>
        <span class="value">{{ currentFPS }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useWaypointStore } from '../stores/WaypointStore.js'
import { WAYPOINT_TYPES } from '../models/WaypointModels.js'
import L from 'leaflet'

export default {
  name: 'WaypointMapLayer',
  props: {
    map: {
      type: Object,
      required: true
    },
    showPerformanceStats: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const waypointStore = useWaypointStore()
    
    // 响应式数据
    const selectedWaypoint = ref(null)
    const popupPosition = ref({ x: 0, y: 0 })
    const currentFPS = ref(60)
    const lastFrameTime = ref(performance.now())
    const frameCount = ref(0)
    
    // 地图图层
    const waypointLayer = ref(null)
    const pathLayer = ref(null)
    const clusterLayer = ref(null)
    
    // 标记缓存
    const waypointMarkers = new Map()
    const clusterMarkers = new Map()
    
    // 计算属性
    const isCreatingWaypoint = computed(() => waypointStore.state.isCreatingWaypoint)
    const waypointCreationMode = computed(() => waypointStore.state.waypointCreationMode)
    const visibleWaypoints = computed(() => waypointStore.state.visibleWaypoints)
    const waypointClusters = computed(() => waypointStore.state.waypointClusters)
    const bulkEditMode = computed(() => waypointStore.state.bulkEditMode)
    const bulkEditSelection = computed(() => waypointStore.state.bulkEditSelection)
    const showWaypointLabels = computed(() => waypointStore.state.showWaypointLabels)
    const showWaypointConnections = computed(() => waypointStore.state.showWaypointConnections)
    
    const waypointTypeInfo = computed(() => {
      return WAYPOINT_TYPES[waypointCreationMode.value] || WAYPOINT_TYPES.WAYPOINT
    })
    
    const popupStyle = computed(() => ({
      left: popupPosition.value.x + 'px',
      top: popupPosition.value.y + 'px'
    }))
    
    // 生命周期
    onMounted(() => {
      initMapLayers()
      setupEventListeners()
      startPerformanceMonitoring()
    })
    
    onUnmounted(() => {
      cleanup()
    })
    
    // 监听器
    watch(() => waypointStore.state.waypoints, updateWaypointMarkers, { deep: true })
    watch(() => waypointStore.state.visibleWaypoints, updateVisibleMarkers)
    watch(() => waypointStore.state.waypointClusters, updateClusterMarkers)
    watch(() => waypointStore.state.currentMission, updateMissionPath)
    
    // 初始化地图图层
    function initMapLayers() {
      // 创建图层组
      waypointLayer.value = L.layerGroup().addTo(props.map)
      pathLayer.value = L.layerGroup().addTo(props.map)
      clusterLayer.value = L.layerGroup().addTo(props.map)
      
      // 设置图层顺序
      waypointLayer.value.setZIndex(1000)
      pathLayer.value.setZIndex(999)
      clusterLayer.value.setZIndex(1001)
    }
    
    // 设置事件监听器
    function setupEventListeners() {
      // 地图点击事件
      props.map.on('click', handleMapClick)
      props.map.on('moveend', handleMapMoveEnd)
      props.map.on('zoomend', handleMapZoomEnd)
      
      // 航点存储事件
      waypointStore.on('waypointCreated', handleWaypointCreated)
      waypointStore.on('waypointUpdated', handleWaypointUpdated)
      waypointStore.on('waypointDeleted', handleWaypointDeleted)
      waypointStore.on('visibleWaypointsUpdated', updateVisibleMarkers)
      waypointStore.on('waypointsClustered', updateClusterMarkers)
    }
    
    // 地图点击处理
    function handleMapClick(e) {
      if (isCreatingWaypoint.value) {
        createWaypointAtPosition(e.latlng)
      } else {
        closePopup()
      }
    }
    
    // 在指定位置创建航点
    function createWaypointAtPosition(latlng) {
      try {
        const waypoint = waypointStore.createWaypoint({
          latitude: latlng.lat,
          longitude: latlng.lng,
          type: waypointCreationMode.value,
          altitude: 50, // 默认高度
          speed: 5.0    // 默认速度
        })
        
        // 退出创建模式
        waypointStore.state.isCreatingWaypoint = false
        
        // 选中新创建的航点
        selectWaypoint(waypoint, latlng)
        
      } catch (error) {
        console.error('创建航点失败:', error)
        // 显示错误提示
      }
    }
    
    // 更新航点标记
    function updateWaypointMarkers() {
      // 清除现有标记
      waypointMarkers.forEach(marker => {
        waypointLayer.value.removeLayer(marker)
      })
      waypointMarkers.clear()
      
      // 创建新标记
      waypointStore.state.waypoints.forEach((waypoint, id) => {
        if (visibleWaypoints.value.has(id)) {
          createWaypointMarker(waypoint)
        }
      })
    }
    
    // 创建航点标记
    function createWaypointMarker(waypoint) {
      const typeInfo = WAYPOINT_TYPES[waypoint.type] || WAYPOINT_TYPES.WAYPOINT
      
      // 创建自定义图标
      const icon = L.divIcon({
        className: 'waypoint-marker',
        html: `
          <div class="waypoint-icon ${waypoint.status.toLowerCase()}" 
               style="background-color: ${typeInfo.color}">
            <i class="${typeInfo.icon}"></i>
            ${showWaypointLabels.value ? `<span class="waypoint-label">${waypoint.name}</span>` : ''}
          </div>
        `,
        iconSize: [24, 24],
        iconAnchor: [12, 12]
      })
      
      // 创建标记
      const marker = L.marker([waypoint.latitude, waypoint.longitude], { icon })
        .on('click', (e) => {
          e.originalEvent.stopPropagation()
          selectWaypoint(waypoint, e.latlng)
        })
        .on('contextmenu', (e) => {
          e.originalEvent.preventDefault()
          showWaypointContextMenu(waypoint, e.originalEvent)
        })
      
      // 如果支持拖拽
      if (waypoint.type !== 'TAKEOFF') {
        marker.dragging.enable()
        marker.on('dragend', (e) => {
          const newPos = e.target.getLatLng()
          waypointStore.updateWaypoint(waypoint.id, {
            latitude: newPos.lat,
            longitude: newPos.lng
          })
        })
      }
      
      waypointMarkers.set(waypoint.id, marker)
      waypointLayer.value.addLayer(marker)
      
      return marker
    }
    
    // 更新可见标记
    function updateVisibleMarkers() {
      // 隐藏不可见的标记
      waypointMarkers.forEach((marker, id) => {
        if (!visibleWaypoints.value.has(id)) {
          waypointLayer.value.removeLayer(marker)
        }
      })
      
      // 显示可见的标记
      visibleWaypoints.value.forEach(id => {
        if (!waypointMarkers.has(id)) {
          const waypoint = waypointStore.state.waypoints.get(id)
          if (waypoint) {
            createWaypointMarker(waypoint)
          }
        } else {
          const marker = waypointMarkers.get(id)
          if (!waypointLayer.value.hasLayer(marker)) {
            waypointLayer.value.addLayer(marker)
          }
        }
      })
    }
    
    // 更新聚合标记
    function updateClusterMarkers() {
      // 清除现有聚合标记
      clusterMarkers.forEach(marker => {
        clusterLayer.value.removeLayer(marker)
      })
      clusterMarkers.clear()
      
      // 创建新的聚合标记
      waypointClusters.value.forEach(cluster => {
        if (cluster.count > 1) {
          const icon = L.divIcon({
            className: 'waypoint-cluster',
            html: `<div class="cluster-icon">${cluster.count}</div>`,
            iconSize: [40, 40],
            iconAnchor: [20, 20]
          })
          
          const marker = L.marker([cluster.centerLat, cluster.centerLng], { icon })
            .on('click', () => {
              // 缩放到聚合区域
              const group = new L.featureGroup(
                cluster.waypoints.map(wp => 
                  L.marker([wp.latitude, wp.longitude])
                )
              )
              props.map.fitBounds(group.getBounds(), { padding: [20, 20] })
            })
          
          clusterMarkers.set(cluster.id, marker)
          clusterLayer.value.addLayer(marker)
        }
      })
    }
    
    // 选中航点
    function selectWaypoint(waypoint, latlng) {
      selectedWaypoint.value = waypoint
      
      // 计算弹窗位置
      const point = props.map.latLngToContainerPoint(latlng)
      popupPosition.value = {
        x: point.x + 10,
        y: point.y - 10
      }
    }
    
    // 关闭弹窗
    function closePopup() {
      selectedWaypoint.value = null
    }
    
    // 地图移动结束处理
    function handleMapMoveEnd() {
      const bounds = props.map.getBounds()
      waypointStore.updateVisibleWaypoints({
        north: bounds.getNorth(),
        south: bounds.getSouth(),
        east: bounds.getEast(),
        west: bounds.getWest()
      })
    }
    
    // 地图缩放结束处理
    function handleMapZoomEnd() {
      const zoomLevel = props.map.getZoom()
      waypointStore.clusterWaypoints(zoomLevel)
      handleMapMoveEnd() // 同时更新可见航点
    }
    
    // 性能监控
    function startPerformanceMonitoring() {
      function updateFPS() {
        const now = performance.now()
        frameCount.value++
        
        if (now - lastFrameTime.value >= 1000) {
          currentFPS.value = Math.round((frameCount.value * 1000) / (now - lastFrameTime.value))
          frameCount.value = 0
          lastFrameTime.value = now
        }
        
        requestAnimationFrame(updateFPS)
      }
      
      updateFPS()
    }
    
    // 工具函数
    function getWaypointTypeInfo(type) {
      return WAYPOINT_TYPES[type] || WAYPOINT_TYPES.WAYPOINT
    }
    
    function formatCoordinates(waypoint) {
      return `${waypoint.latitude.toFixed(6)}, ${waypoint.longitude.toFixed(6)}`
    }
    
    function cancelCreation() {
      waypointStore.state.isCreatingWaypoint = false
    }
    
    function editWaypoint() {
      waypointStore.state.editingWaypoint = selectedWaypoint.value
      closePopup()
    }
    
    function deleteWaypoint() {
      if (confirm(`确定要删除航点 "${selectedWaypoint.value.name}" 吗？`)) {
        waypointStore.deleteWaypoint(selectedWaypoint.value.id)
        closePopup()
      }
    }
    
    // 批量操作
    function bulkDelete() {
      if (confirm(`确定要删除选中的 ${bulkEditSelection.value.size} 个航点吗？`)) {
        waypointStore.deleteWaypointsBatch(Array.from(bulkEditSelection.value))
        exitBulkMode()
      }
    }
    
    function bulkEdit() {
      // 打开批量编辑对话框
      console.log('批量编辑功能待实现')
    }
    
    function createMissionFromSelection() {
      // 从选中的航点创建任务
      console.log('从选中航点创建任务功能待实现')
    }
    
    function exitBulkMode() {
      waypointStore.state.bulkEditMode = false
      waypointStore.state.bulkEditSelection.clear()
    }
    
    // 清理
    function cleanup() {
      if (props.map) {
        props.map.off('click', handleMapClick)
        props.map.off('moveend', handleMapMoveEnd)
        props.map.off('zoomend', handleMapZoomEnd)
      }
      
      waypointStore.off('waypointCreated', handleWaypointCreated)
      waypointStore.off('waypointUpdated', handleWaypointUpdated)
      waypointStore.off('waypointDeleted', handleWaypointDeleted)
    }
    
    // 事件处理器
    function handleWaypointCreated(waypoint) {
      createWaypointMarker(waypoint)
    }
    
    function handleWaypointUpdated(waypoint) {
      const marker = waypointMarkers.get(waypoint.id)
      if (marker) {
        // 更新标记位置
        marker.setLatLng([waypoint.latitude, waypoint.longitude])
        
        // 更新图标
        const typeInfo = WAYPOINT_TYPES[waypoint.type] || WAYPOINT_TYPES.WAYPOINT
        const icon = L.divIcon({
          className: 'waypoint-marker',
          html: `
            <div class="waypoint-icon ${waypoint.status.toLowerCase()}" 
                 style="background-color: ${typeInfo.color}">
              <i class="${typeInfo.icon}"></i>
              ${showWaypointLabels.value ? `<span class="waypoint-label">${waypoint.name}</span>` : ''}
            </div>
          `,
          iconSize: [24, 24],
          iconAnchor: [12, 12]
        })
        marker.setIcon(icon)
      }
    }
    
    function handleWaypointDeleted(waypoint) {
      const marker = waypointMarkers.get(waypoint.id)
      if (marker) {
        waypointLayer.value.removeLayer(marker)
        waypointMarkers.delete(waypoint.id)
      }
    }
    
    function updateMissionPath() {
      // 清除现有路径
      pathLayer.value.clearLayers()
      
      const mission = waypointStore.state.currentMission
      if (!mission || !showWaypointConnections.value) return
      
      // 创建路径线
      const waypoints = mission.waypoints
      if (waypoints.length > 1) {
        const latlngs = waypoints.map(wp => [wp.latitude, wp.longitude])
        const polyline = L.polyline(latlngs, {
          color: '#3498db',
          weight: 3,
          opacity: 0.7,
          dashArray: '5, 10'
        })
        
        pathLayer.value.addLayer(polyline)
      }
    }
    
    return {
      // 响应式数据
      selectedWaypoint,
      popupStyle,
      currentFPS,
      
      // 计算属性
      isCreatingWaypoint,
      waypointTypeInfo,
      visibleWaypoints,
      waypointClusters,
      bulkEditMode,
      bulkEditSelection,
      
      // 方法
      getWaypointTypeInfo,
      formatCoordinates,
      cancelCreation,
      closePopup,
      editWaypoint,
      deleteWaypoint,
      bulkDelete,
      bulkEdit,
      createMissionFromSelection,
      exitBulkMode
    }
  }
}
</script>

<style scoped>
.waypoint-map-layer {
  position: relative;
  pointer-events: none;
}

/* 创建模式提示 */
.creation-hint {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: #00ffff;
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 2000;
  pointer-events: auto;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 255, 255, 0.3);
  font-size: 14px;
  font-weight: 500;
}

.cancel-btn {
  background: rgba(255, 107, 71, 0.2);
  border: 1px solid rgba(255, 107, 71, 0.5);
  color: #ff6b47;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: rgba(255, 107, 71, 0.3);
  border-color: rgba(255, 107, 71, 0.7);
}

/* 航点弹窗 */
.waypoint-popup {
  position: fixed;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0;
  z-index: 2000;
  pointer-events: auto;
  backdrop-filter: blur(15px);
  min-width: 250px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  background: rgba(0, 255, 255, 0.1);
}

.popup-header h4 {
  margin: 0;
  color: #00ffff;
  font-size: 14px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  color: #ff6b47;
  background: rgba(255, 107, 71, 0.1);
}

.popup-content {
  padding: 12px 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
}

.info-row .label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.info-row .value {
  color: #ffffff;
  font-weight: 600;
}

.status-pending { color: #f39c12; }
.status-active { color: #2ecc71; }
.status-completed { color: #27ae60; }
.status-failed { color: #e74c3c; }
.status-skipped { color: #95a5a6; }

.popup-actions {
  display: flex;
  gap: 8px;
  padding: 12px 16px;
  border-top: 1px solid rgba(0, 255, 255, 0.2);
  background: rgba(0, 0, 0, 0.3);
}

.edit-btn, .delete-btn {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.edit-btn {
  background: rgba(52, 152, 219, 0.1);
  border-color: rgba(52, 152, 219, 0.5);
  color: #3498db;
}

.edit-btn:hover {
  background: rgba(52, 152, 219, 0.2);
  border-color: rgba(52, 152, 219, 0.7);
}

.delete-btn {
  background: rgba(231, 76, 60, 0.1);
  border-color: rgba(231, 76, 60, 0.5);
  color: #e74c3c;
}

.delete-btn:hover {
  background: rgba(231, 76, 60, 0.2);
  border-color: rgba(231, 76, 60, 0.7);
}

/* 批量操作工具栏 */
.bulk-edit-toolbar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 12px;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  z-index: 2000;
  pointer-events: auto;
  backdrop-filter: blur(15px);
}

.selection-info {
  color: #00ffff;
  font-size: 14px;
  font-weight: 500;
}

.bulk-actions {
  display: flex;
  gap: 8px;
}

.bulk-btn {
  padding: 8px 12px;
  border: 1px solid;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.bulk-btn.delete {
  background: rgba(231, 76, 60, 0.1);
  border-color: rgba(231, 76, 60, 0.5);
  color: #e74c3c;
}

.bulk-btn.edit {
  background: rgba(52, 152, 219, 0.1);
  border-color: rgba(52, 152, 219, 0.5);
  color: #3498db;
}

.bulk-btn.mission {
  background: rgba(46, 204, 113, 0.1);
  border-color: rgba(46, 204, 113, 0.5);
  color: #2ecc71;
}

.bulk-btn.cancel {
  background: rgba(149, 165, 166, 0.1);
  border-color: rgba(149, 165, 166, 0.5);
  color: #95a5a6;
}

.bulk-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 性能统计 */
.performance-stats {
  position: fixed;
  top: 80px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 8px;
  padding: 12px;
  z-index: 2000;
  pointer-events: auto;
  backdrop-filter: blur(10px);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 11px;
  min-width: 80px;
}

.stat-item .label {
  color: rgba(255, 255, 255, 0.7);
}

.stat-item .value {
  color: #00ffff;
  font-weight: 600;
}
</style>

<style>
/* 全局样式 - 航点标记 */
.waypoint-marker {
  background: none !important;
  border: none !important;
}

.waypoint-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  position: relative;
  transition: all 0.3s ease;
}

.waypoint-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
}

.waypoint-icon.pending {
  border-color: #f39c12;
  box-shadow: 0 0 10px rgba(243, 156, 18, 0.5);
}

.waypoint-icon.active {
  border-color: #2ecc71;
  box-shadow: 0 0 10px rgba(46, 204, 113, 0.5);
  animation: pulse-active 2s infinite;
}

.waypoint-icon.completed {
  border-color: #27ae60;
  opacity: 0.8;
}

.waypoint-icon.failed {
  border-color: #e74c3c;
  box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
}

@keyframes pulse-active {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.waypoint-label {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  white-space: nowrap;
  margin-top: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 聚合标记 */
.waypoint-cluster {
  background: none !important;
  border: none !important;
}

.cluster-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(52, 152, 219, 0.8);
  border: 3px solid rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.cluster-icon:hover {
  transform: scale(1.1);
  background: rgba(52, 152, 219, 1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5);
}
</style>
