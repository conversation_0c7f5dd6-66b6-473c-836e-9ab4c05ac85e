<template>
  <div class="virtual-list" ref="container" @scroll="handleScroll">
    <div class="virtual-list-phantom" :style="{ height: totalHeight + 'px' }"></div>
    <div class="virtual-list-content" :style="{ transform: `translateY(${startOffset}px)` }">
      <div
        v-for="item in visibleItems"
        :key="getItemKey(item)"
        class="virtual-list-item"
        :style="{ height: itemHeight + 'px' }"
      >
        <slot :item="item" :index="item.index"></slot>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

export default {
  name: 'VirtualList',
  props: {
    items: {
      type: Array,
      required: true
    },
    itemHeight: {
      type: Number,
      default: 50
    },
    containerHeight: {
      type: Number,
      default: 300
    },
    buffer: {
      type: Number,
      default: 5
    }
  },
  setup(props) {
    const container = ref(null)
    const scrollTop = ref(0)
    
    // 计算属性
    const totalHeight = computed(() => props.items.length * props.itemHeight)
    
    const visibleCount = computed(() => Math.ceil(props.containerHeight / props.itemHeight))
    
    const startIndex = computed(() => {
      const index = Math.floor(scrollTop.value / props.itemHeight)
      return Math.max(0, index - props.buffer)
    })
    
    const endIndex = computed(() => {
      const index = startIndex.value + visibleCount.value + props.buffer * 2
      return Math.min(props.items.length - 1, index)
    })
    
    const startOffset = computed(() => startIndex.value * props.itemHeight)
    
    const visibleItems = computed(() => {
      const items = []
      for (let i = startIndex.value; i <= endIndex.value; i++) {
        if (props.items[i]) {
          items.push({
            ...props.items[i],
            index: i
          })
        }
      }
      return items
    })
    
    // 方法
    function handleScroll(e) {
      scrollTop.value = e.target.scrollTop
    }
    
    function getItemKey(item) {
      return item.id || item.index
    }
    
    function scrollToIndex(index) {
      if (container.value) {
        const targetScrollTop = index * props.itemHeight
        container.value.scrollTop = targetScrollTop
      }
    }
    
    // 生命周期
    onMounted(() => {
      if (container.value) {
        container.value.style.height = props.containerHeight + 'px'
        container.value.style.overflowY = 'auto'
      }
    })
    
    // 监听容器高度变化
    watch(() => props.containerHeight, (newHeight) => {
      if (container.value) {
        container.value.style.height = newHeight + 'px'
      }
    })
    
    return {
      container,
      totalHeight,
      startOffset,
      visibleItems,
      handleScroll,
      getItemKey,
      scrollToIndex
    }
  }
}
</script>

<style scoped>
.virtual-list {
  position: relative;
  overflow-y: auto;
}

.virtual-list-phantom {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
}

.virtual-list-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.virtual-list-item {
  box-sizing: border-box;
}
</style>
