// 航点状态管理系统
import { reactive, computed, ref } from 'vue'
import { Waypoint, Mission, WAYPOINT_TYPES, MISSION_STATUS } from '../models/WaypointModels.js'

class WaypointStore {
  constructor() {
    // 响应式状态
    this.state = reactive({
      // 航点数据
      waypoints: new Map(),
      missions: new Map(),
      currentMission: null,
      
      // UI状态
      selectedWaypoints: new Set(),
      editingWaypoint: null,
      isCreatingWaypoint: false,
      waypointCreationMode: 'WAYPOINT',
      
      // 地图状态
      mapCenter: [39.9042, 116.4074],
      mapZoom: 13,
      showWaypointLabels: true,
      showWaypointConnections: true,
      
      // 性能优化
      visibleWaypoints: new Set(),
      waypointClusters: new Map(),
      renderBounds: null,
      
      // 批量操作
      bulkEditMode: false,
      bulkEditSelection: new Set(),
      
      // 搜索和过滤
      searchQuery: '',
      filterType: 'ALL',
      filterStatus: 'ALL',
      
      // 统计信息
      totalWaypoints: 0,
      totalMissions: 0,
      activeMissions: 0
    })
    
    // 性能配置
    this.config = {
      maxVisibleWaypoints: 1000,
      clusterDistance: 50,
      renderThrottle: 16, // 60fps
      batchSize: 100
    }
    
    // 事件监听器
    this.listeners = new Map()
    
    // 初始化
    this.init()
  }
  
  init() {
    // 加载本地存储的数据
    this.loadFromStorage()
    
    // 设置自动保存
    this.setupAutoSave()
    
    // 初始化性能监控
    this.setupPerformanceMonitoring()
  }
  
  // ==================== 航点管理 ====================
  
  // 创建航点
  createWaypoint(options) {
    const waypoint = new Waypoint(options)
    const validation = waypoint.validate()
    
    if (!validation.isValid) {
      throw new Error(`航点验证失败: ${validation.errors.join(', ')}`)
    }
    
    this.state.waypoints.set(waypoint.id, waypoint)
    this.state.totalWaypoints++
    
    this.emit('waypointCreated', waypoint)
    this.saveToStorage()
    
    return waypoint
  }
  
  // 批量创建航点
  createWaypointsBatch(waypointOptions) {
    const waypoints = []
    const errors = []
    
    // 分批处理以避免阻塞UI
    const processBatch = (batch) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          batch.forEach((options, index) => {
            try {
              const waypoint = this.createWaypoint(options)
              waypoints.push(waypoint)
            } catch (error) {
              errors.push({ index, error: error.message })
            }
          })
          resolve()
        }, 0)
      })
    }
    
    // 分批处理
    const batches = []
    for (let i = 0; i < waypointOptions.length; i += this.config.batchSize) {
      batches.push(waypointOptions.slice(i, i + this.config.batchSize))
    }
    
    return Promise.all(batches.map(processBatch)).then(() => ({
      waypoints,
      errors
    }))
  }
  
  // 更新航点
  updateWaypoint(waypointId, updates) {
    const waypoint = this.state.waypoints.get(waypointId)
    if (!waypoint) {
      throw new Error(`航点不存在: ${waypointId}`)
    }
    
    // 应用更新
    Object.assign(waypoint, updates, { updatedAt: new Date() })
    
    // 验证更新后的航点
    const validation = waypoint.validate()
    if (!validation.isValid) {
      throw new Error(`航点验证失败: ${validation.errors.join(', ')}`)
    }
    
    this.emit('waypointUpdated', waypoint)
    this.saveToStorage()
    
    return waypoint
  }
  
  // 删除航点
  deleteWaypoint(waypointId) {
    const waypoint = this.state.waypoints.get(waypointId)
    if (!waypoint) return false
    
    // 从所有任务中移除
    this.state.missions.forEach(mission => {
      mission.removeWaypoint(waypointId)
    })
    
    // 从选择中移除
    this.state.selectedWaypoints.delete(waypointId)
    this.state.bulkEditSelection.delete(waypointId)
    
    // 删除航点
    this.state.waypoints.delete(waypointId)
    this.state.totalWaypoints--
    
    this.emit('waypointDeleted', waypoint)
    this.saveToStorage()
    
    return true
  }
  
  // 批量删除航点
  deleteWaypointsBatch(waypointIds) {
    const deletedWaypoints = []
    
    waypointIds.forEach(id => {
      const waypoint = this.state.waypoints.get(id)
      if (waypoint) {
        deletedWaypoints.push(waypoint)
        this.deleteWaypoint(id)
      }
    })
    
    this.emit('waypointsBatchDeleted', deletedWaypoints)
    return deletedWaypoints
  }
  
  // ==================== 任务管理 ====================
  
  // 创建任务
  createMission(options) {
    const mission = new Mission(options)
    const validation = mission.validate()
    
    if (!validation.isValid) {
      throw new Error(`任务验证失败: ${validation.errors.join(', ')}`)
    }
    
    this.state.missions.set(mission.id, mission)
    this.state.totalMissions++
    
    if (mission.status === 'RUNNING') {
      this.state.activeMissions++
    }
    
    this.emit('missionCreated', mission)
    this.saveToStorage()
    
    return mission
  }
  
  // 更新任务
  updateMission(missionId, updates) {
    const mission = this.state.missions.get(missionId)
    if (!mission) {
      throw new Error(`任务不存在: ${missionId}`)
    }
    
    const oldStatus = mission.status
    Object.assign(mission, updates, { updatedAt: new Date() })
    
    // 更新活跃任务计数
    if (oldStatus !== mission.status) {
      if (oldStatus === 'RUNNING') this.state.activeMissions--
      if (mission.status === 'RUNNING') this.state.activeMissions++
    }
    
    this.emit('missionUpdated', mission)
    this.saveToStorage()
    
    return mission
  }
  
  // 设置当前任务
  setCurrentMission(missionId) {
    const mission = this.state.missions.get(missionId)
    if (!mission) {
      throw new Error(`任务不存在: ${missionId}`)
    }
    
    this.state.currentMission = mission
    this.emit('currentMissionChanged', mission)
    
    return mission
  }
  
  // ==================== 性能优化 ====================
  
  // 更新可见航点（基于地图视口）
  updateVisibleWaypoints(bounds) {
    this.state.renderBounds = bounds
    this.state.visibleWaypoints.clear()
    
    let visibleCount = 0
    
    for (const [id, waypoint] of this.state.waypoints) {
      if (visibleCount >= this.config.maxVisibleWaypoints) break
      
      if (this.isWaypointInBounds(waypoint, bounds)) {
        this.state.visibleWaypoints.add(id)
        visibleCount++
      }
    }
    
    this.emit('visibleWaypointsUpdated', this.state.visibleWaypoints)
  }
  
  // 检查航点是否在边界内
  isWaypointInBounds(waypoint, bounds) {
    return waypoint.latitude >= bounds.south &&
           waypoint.latitude <= bounds.north &&
           waypoint.longitude >= bounds.west &&
           waypoint.longitude <= bounds.east
  }
  
  // 航点聚合
  clusterWaypoints(zoomLevel) {
    this.state.waypointClusters.clear()
    
    if (zoomLevel > 12) {
      // 高缩放级别，不聚合
      return
    }
    
    const clusters = new Map()
    const clusterDistance = this.config.clusterDistance / Math.pow(2, zoomLevel)
    
    for (const [id, waypoint] of this.state.waypoints) {
      if (!this.state.visibleWaypoints.has(id)) continue
      
      let clustered = false
      
      for (const [clusterId, cluster] of clusters) {
        const distance = this.calculateDistance(
          waypoint.latitude, waypoint.longitude,
          cluster.centerLat, cluster.centerLng
        )
        
        if (distance < clusterDistance) {
          cluster.waypoints.push(waypoint)
          cluster.count++
          clustered = true
          break
        }
      }
      
      if (!clustered) {
        const clusterId = `cluster_${clusters.size}`
        clusters.set(clusterId, {
          id: clusterId,
          centerLat: waypoint.latitude,
          centerLng: waypoint.longitude,
          waypoints: [waypoint],
          count: 1
        })
      }
    }
    
    this.state.waypointClusters = clusters
    this.emit('waypointsClustered', clusters)
  }
  
  // 计算两点间距离
  calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371000 // 地球半径
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLng = (lng2 - lng1) * Math.PI / 180
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c
  }
  
  // ==================== 数据持久化 ====================
  
  // 保存到本地存储
  saveToStorage() {
    try {
      const data = {
        waypoints: Array.from(this.state.waypoints.values()),
        missions: Array.from(this.state.missions.values()),
        currentMissionId: this.state.currentMission?.id || null,
        timestamp: Date.now()
      }
      
      localStorage.setItem('droneWaypointData', JSON.stringify(data))
    } catch (error) {
      console.error('保存数据失败:', error)
    }
  }
  
  // 从本地存储加载
  loadFromStorage() {
    try {
      const data = JSON.parse(localStorage.getItem('droneWaypointData') || '{}')
      
      if (data.waypoints) {
        data.waypoints.forEach(wpData => {
          const waypoint = new Waypoint(wpData)
          this.state.waypoints.set(waypoint.id, waypoint)
        })
        this.state.totalWaypoints = this.state.waypoints.size
      }
      
      if (data.missions) {
        data.missions.forEach(missionData => {
          const mission = new Mission(missionData)
          this.state.missions.set(mission.id, mission)
          
          if (mission.status === 'RUNNING') {
            this.state.activeMissions++
          }
        })
        this.state.totalMissions = this.state.missions.size
      }
      
      if (data.currentMissionId) {
        this.state.currentMission = this.state.missions.get(data.currentMissionId)
      }
      
    } catch (error) {
      console.error('加载数据失败:', error)
    }
  }
  
  // 设置自动保存
  setupAutoSave() {
    setInterval(() => {
      this.saveToStorage()
    }, 30000) // 每30秒自动保存
  }
  
  // 性能监控
  setupPerformanceMonitoring() {
    setInterval(() => {
      const stats = {
        waypointCount: this.state.waypoints.size,
        visibleWaypointCount: this.state.visibleWaypoints.size,
        clusterCount: this.state.waypointClusters.size,
        memoryUsage: performance.memory?.usedJSHeapSize || 0
      }
      
      this.emit('performanceStats', stats)
    }, 5000)
  }
  
  // ==================== 事件系统 ====================
  
  // 添加事件监听器
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    this.listeners.get(event).add(callback)
  }
  
  // 移除事件监听器
  off(event, callback) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(callback)
    }
  }
  
  // 触发事件
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`事件处理器错误 [${event}]:`, error)
        }
      })
    }
  }
  
  // ==================== 计算属性 ====================
  
  // 获取过滤后的航点
  get filteredWaypoints() {
    return computed(() => {
      let waypoints = Array.from(this.state.waypoints.values())
      
      // 搜索过滤
      if (this.state.searchQuery) {
        const query = this.state.searchQuery.toLowerCase()
        waypoints = waypoints.filter(wp => 
          wp.name.toLowerCase().includes(query) ||
          wp.id.toLowerCase().includes(query)
        )
      }
      
      // 类型过滤
      if (this.state.filterType !== 'ALL') {
        waypoints = waypoints.filter(wp => wp.type === this.state.filterType)
      }
      
      // 状态过滤
      if (this.state.filterStatus !== 'ALL') {
        waypoints = waypoints.filter(wp => wp.status === this.state.filterStatus)
      }
      
      return waypoints
    })
  }
  
  // 获取当前任务的航点
  get currentMissionWaypoints() {
    return computed(() => {
      return this.state.currentMission?.waypoints || []
    })
  }
}

// 创建全局实例
export const waypointStore = new WaypointStore()

// 导出响应式状态
export const useWaypointStore = () => {
  return {
    state: waypointStore.state,
    createWaypoint: waypointStore.createWaypoint.bind(waypointStore),
    updateWaypoint: waypointStore.updateWaypoint.bind(waypointStore),
    deleteWaypoint: waypointStore.deleteWaypoint.bind(waypointStore),
    createMission: waypointStore.createMission.bind(waypointStore),
    updateMission: waypointStore.updateMission.bind(waypointStore),
    setCurrentMission: waypointStore.setCurrentMission.bind(waypointStore),
    updateVisibleWaypoints: waypointStore.updateVisibleWaypoints.bind(waypointStore),
    clusterWaypoints: waypointStore.clusterWaypoints.bind(waypointStore),
    on: waypointStore.on.bind(waypointStore),
    off: waypointStore.off.bind(waypointStore),
    filteredWaypoints: waypointStore.filteredWaypoints,
    currentMissionWaypoints: waypointStore.currentMissionWaypoints
  }
}
