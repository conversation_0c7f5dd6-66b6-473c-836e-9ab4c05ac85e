<template>
  <div class="dialog-overlay" @click="handleOverlayClick">
    <div class="waypoint-edit-dialog" @click.stop>
      <div class="dialog-header">
        <h3>编辑航点</h3>
        <button @click="$emit('close')" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="dialog-content">
        <form @submit.prevent="updateWaypoint">
          <div class="form-grid">
            <!-- 基本信息 -->
            <div class="form-section">
              <h4>基本信息</h4>
              <div class="form-group">
                <label>名称</label>
                <input v-model="form.name" type="text" required />
              </div>
              
              <div class="form-group">
                <label>类型</label>
                <select v-model="form.type" required>
                  <option v-for="(info, type) in WAYPOINT_TYPES" :key="type" :value="type">
                    {{ info.name }}
                  </option>
                </select>
              </div>
              
              <div class="form-group">
                <label>状态</label>
                <select v-model="form.status">
                  <option value="PENDING">待执行</option>
                  <option value="ACTIVE">执行中</option>
                  <option value="COMPLETED">已完成</option>
                  <option value="FAILED">失败</option>
                  <option value="SKIPPED">跳过</option>
                </select>
              </div>
            </div>
            
            <!-- 位置信息 -->
            <div class="form-section">
              <h4>位置信息</h4>
              <div class="form-row">
                <div class="form-group">
                  <label>纬度</label>
                  <input v-model.number="form.latitude" type="number" step="0.000001" required />
                </div>
                <div class="form-group">
                  <label>经度</label>
                  <input v-model.number="form.longitude" type="number" step="0.000001" required />
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label>海拔高度 (m)</label>
                  <input v-model.number="form.altitude" type="number" step="0.1" required />
                </div>
                <div class="form-group">
                  <label>相对高度 (m)</label>
                  <input v-model.number="form.relativeAltitude" type="number" step="0.1" />
                </div>
              </div>
            </div>
            
            <!-- 飞行参数 -->
            <div class="form-section">
              <h4>飞行参数</h4>
              <div class="form-row">
                <div class="form-group">
                  <label>速度 (m/s)</label>
                  <input v-model.number="form.speed" type="number" step="0.1" min="0" max="30" required />
                </div>
                <div class="form-group">
                  <label>航向角 (度)</label>
                  <input v-model.number="form.heading" type="number" step="1" min="0" max="360" />
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label>接受半径 (m)</label>
                  <input v-model.number="form.acceptanceRadius" type="number" step="0.1" min="0.5" max="50" />
                </div>
                <div class="form-group">
                  <label>停留时间 (秒)</label>
                  <input v-model.number="form.holdTime" type="number" step="1" min="0" />
                </div>
              </div>
            </div>
            
            <!-- 相机参数 -->
            <div class="form-section" v-if="showCameraParams">
              <h4>相机参数</h4>
              <div class="form-row">
                <div class="form-group">
                  <label>云台俯仰角 (度)</label>
                  <input v-model.number="form.gimbalPitch" type="number" step="1" min="-90" max="90" />
                </div>
                <div class="form-group">
                  <label>云台偏航角 (度)</label>
                  <input v-model.number="form.gimbalYaw" type="number" step="1" min="-180" max="180" />
                </div>
              </div>
              
              <div class="form-group">
                <label>相机动作</label>
                <select v-model="form.cameraAction">
                  <option value="NONE">无动作</option>
                  <option value="PHOTO">拍照</option>
                  <option value="VIDEO_START">开始录像</option>
                  <option value="VIDEO_STOP">停止录像</option>
                </select>
              </div>
            </div>
          </div>
          
          <!-- 变更历史 -->
          <div class="history-section" v-if="hasChanges">
            <h4>变更内容</h4>
            <div class="changes-list">
              <div v-for="change in changes" :key="change.field" class="change-item">
                <span class="field">{{ change.field }}:</span>
                <span class="old-value">{{ change.oldValue }}</span>
                <i class="fas fa-arrow-right"></i>
                <span class="new-value">{{ change.newValue }}</span>
              </div>
            </div>
          </div>
          
          <div class="dialog-actions">
            <button type="button" @click="resetForm" class="btn-reset">
              重置
            </button>
            <button type="button" @click="$emit('close')" class="btn-cancel">
              取消
            </button>
            <button type="submit" class="btn-update" :disabled="!isFormValid || !hasChanges">
              更新航点
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useWaypointStore } from '../stores/WaypointStore.js'
import { WAYPOINT_TYPES } from '../models/WaypointModels.js'

export default {
  name: 'WaypointEditDialog',
  props: {
    waypoint: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'updated'],
  setup(props, { emit }) {
    const waypointStore = useWaypointStore()
    
    // 表单数据
    const form = ref({})
    const originalForm = ref({})
    
    // 计算属性
    const showCameraParams = computed(() => {
      return ['PHOTO', 'VIDEO_START', 'VIDEO_STOP'].includes(form.value.type)
    })
    
    const isFormValid = computed(() => {
      return form.value.name?.trim() !== '' &&
             form.value.latitude >= -90 && form.value.latitude <= 90 &&
             form.value.longitude >= -180 && form.value.longitude <= 180 &&
             form.value.altitude >= 0 &&
             form.value.speed > 0
    })
    
    const hasChanges = computed(() => {
      return changes.value.length > 0
    })
    
    const changes = computed(() => {
      const changeList = []
      const fieldsToCheck = [
        'name', 'type', 'status', 'latitude', 'longitude', 'altitude', 
        'relativeAltitude', 'speed', 'heading', 'acceptanceRadius', 
        'holdTime', 'gimbalPitch', 'gimbalYaw', 'cameraAction'
      ]
      
      fieldsToCheck.forEach(field => {
        const oldValue = originalForm.value[field]
        const newValue = form.value[field]
        
        if (oldValue !== newValue) {
          changeList.push({
            field: getFieldLabel(field),
            oldValue: formatValue(oldValue),
            newValue: formatValue(newValue)
          })
        }
      })
      
      return changeList
    })
    
    // 方法
    function initForm() {
      const waypoint = props.waypoint
      form.value = {
        name: waypoint.name,
        type: waypoint.type,
        status: waypoint.status,
        latitude: waypoint.latitude,
        longitude: waypoint.longitude,
        altitude: waypoint.altitude,
        relativeAltitude: waypoint.relativeAltitude,
        speed: waypoint.speed,
        heading: waypoint.heading,
        acceptanceRadius: waypoint.acceptanceRadius,
        holdTime: waypoint.holdTime,
        gimbalPitch: waypoint.gimbalPitch,
        gimbalYaw: waypoint.gimbalYaw,
        cameraAction: waypoint.cameraAction
      }
      
      // 保存原始值
      originalForm.value = { ...form.value }
    }
    
    function resetForm() {
      form.value = { ...originalForm.value }
    }
    
    function getFieldLabel(field) {
      const labels = {
        name: '名称',
        type: '类型',
        status: '状态',
        latitude: '纬度',
        longitude: '经度',
        altitude: '海拔高度',
        relativeAltitude: '相对高度',
        speed: '速度',
        heading: '航向角',
        acceptanceRadius: '接受半径',
        holdTime: '停留时间',
        gimbalPitch: '云台俯仰角',
        gimbalYaw: '云台偏航角',
        cameraAction: '相机动作'
      }
      return labels[field] || field
    }
    
    function formatValue(value) {
      if (typeof value === 'number') {
        return value.toFixed(6).replace(/\.?0+$/, '')
      }
      return String(value || '')
    }
    
    function updateWaypoint() {
      try {
        const updatedWaypoint = waypointStore.updateWaypoint(props.waypoint.id, form.value)
        emit('updated', updatedWaypoint)
      } catch (error) {
        alert(`更新航点失败: ${error.message}`)
      }
    }
    
    function handleOverlayClick() {
      if (hasChanges.value) {
        if (confirm('有未保存的更改，确定要关闭吗？')) {
          emit('close')
        }
      } else {
        emit('close')
      }
    }
    
    // 生命周期
    onMounted(() => {
      initForm()
    })
    
    // 监听航点变化
    watch(() => props.waypoint, () => {
      initForm()
    }, { deep: true })
    
    return {
      form,
      originalForm,
      showCameraParams,
      isFormValid,
      hasChanges,
      changes,
      WAYPOINT_TYPES,
      initForm,
      resetForm,
      getFieldLabel,
      formatValue,
      updateWaypoint,
      handleOverlayClick
    }
  }
}
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
  backdrop-filter: blur(5px);
}

.waypoint-edit-dialog {
  background: rgba(15, 25, 40, 0.95);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 12px;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  background: rgba(0, 255, 255, 0.05);
}

.dialog-header h3 {
  margin: 0;
  color: #00ffff;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-size: 16px;
}

.close-btn:hover {
  color: #ff6b47;
  background: rgba(255, 107, 71, 0.1);
}

.dialog-content {
  padding: 24px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.form-section {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
}

.form-section h4 {
  margin: 0 0 16px 0;
  color: #00ffff;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  padding-bottom: 8px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: #ffffff;
  font-size: 13px;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #00ffff;
  box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.2);
}

.history-section {
  background: rgba(255, 193, 7, 0.05);
  border: 1px solid rgba(255, 193, 7, 0.2);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.history-section h4 {
  margin: 0 0 12px 0;
  color: #ffc107;
  font-size: 14px;
  font-weight: 600;
}

.changes-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.change-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  padding: 4px 0;
}

.change-item .field {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  min-width: 80px;
}

.change-item .old-value {
  color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
}

.change-item .new-value {
  color: #2ecc71;
  background: rgba(46, 204, 113, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
}

.change-item i {
  color: rgba(255, 255, 255, 0.5);
  font-size: 10px;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-reset,
.btn-cancel,
.btn-update {
  padding: 10px 20px;
  border: 1px solid;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-reset {
  background: rgba(255, 193, 7, 0.1);
  border-color: rgba(255, 193, 7, 0.5);
  color: #ffc107;
}

.btn-reset:hover {
  background: rgba(255, 193, 7, 0.2);
  border-color: rgba(255, 193, 7, 0.7);
}

.btn-cancel {
  background: rgba(149, 165, 166, 0.1);
  border-color: rgba(149, 165, 166, 0.5);
  color: #95a5a6;
}

.btn-cancel:hover {
  background: rgba(149, 165, 166, 0.2);
  border-color: rgba(149, 165, 166, 0.7);
}

.btn-update {
  background: rgba(52, 152, 219, 0.1);
  border-color: rgba(52, 152, 219, 0.5);
  color: #3498db;
}

.btn-update:hover:not(:disabled) {
  background: rgba(52, 152, 219, 0.2);
  border-color: rgba(52, 152, 219, 0.7);
}

.btn-update:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
}
</style>
